@echo off
chcp 65001 >nul
title Demo Address Comparison Tool

echo.
echo ========================================
echo    🏠 DEMO TOOL SO SÁNH ĐỊA CHỈ
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 Kiểm tra Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python không được tìm thấy!
    pause
    exit /b 1
)

echo ✅ Python đã được cài đặt

echo.
echo 🚀 Chạy demo với dữ liệu mẫu...
echo.

python test_demo_data.py

if errorlevel 1 (
    echo.
    echo ❌ Có lỗi xảy ra trong quá trình demo!
) else (
    echo.
    echo ✅ Demo đã hoàn thành!
)

echo.
echo 💡 Để sử dụng tool với giao diện, chạy: run_address_comparison.bat
echo.
pause
