#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script cuối cùng cho Keras model - test với nhiều ảnh hơn
"""

import os
import sys
import time
import random

# Thêm thư mục gốc vào path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)

def test_final_keras():
    """Test cuối cùng với Keras model"""
    
    try:
        print("🎯 TEST CUỐI CÙNG - KERAS MODEL")
        print("=" * 50)
        
        # Import và load model
        from kiem_tra_mst_qt import load_model, solve_captcha
        
        print("🔄 Đang load model...")
        start_time = time.time()
        
        def progress_callback(progress):
            if progress % 20 == 0:  # Chỉ hiện mỗi 20%
                print(f"   Progress: {progress}%")
        
        load_model(progress_callback)
        load_time = time.time() - start_time
        print(f"✅ Load model thành công trong {load_time:.2f}s")
        
        # Test với train_data
        train_data_dir = os.path.join(BASE_DIR, "train_data")
        if not os.path.exists(train_data_dir):
            print("❌ Không tìm thấy thư mục train_data")
            return
            
        # Lấy tất cả ảnh và random 20 ảnh để test
        all_image_files = [f for f in os.listdir(train_data_dir) if f.endswith('.png')]
        
        if len(all_image_files) < 20:
            test_images = all_image_files
        else:
            test_images = random.sample(all_image_files, 20)
        
        if not test_images:
            print("❌ Không tìm thấy ảnh trong train_data")
            return
            
        print(f"\n🖼️ Test với {len(test_images)} ảnh ngẫu nhiên từ train_data:")
        print("=" * 50)
        
        correct = 0
        total = len(test_images)
        total_time = 0
        
        for i, image_file in enumerate(test_images, 1):
            image_path = os.path.join(train_data_dir, image_file)
            expected_label = os.path.splitext(image_file)[0]
            
            print(f"{i:2d}. {image_file:<12} | Expected: {expected_label:<6}", end=" | ")
            
            # Predict
            start_time = time.time()
            predicted = solve_captcha(image_path)
            predict_time = time.time() - start_time
            total_time += predict_time
            
            print(f"Predicted: {predicted:<6} | {predict_time:.3f}s", end=" | ")
            
            # So sánh kết quả
            if predicted == expected_label:
                print("✅ ĐÚNG")
                correct += 1
            else:
                print("❌ SAI")
        
        # Tổng kết
        print("=" * 50)
        accuracy = (correct / total) * 100
        avg_time = total_time / total
        
        print(f"📊 KẾT QUẢ TỔNG KẾT:")
        print(f"   Tổng số ảnh: {total}")
        print(f"   Đúng: {correct}")
        print(f"   Sai: {total - correct}")
        print(f"   Độ chính xác: {accuracy:.1f}%")
        print(f"   Thời gian trung bình: {avg_time:.3f}s/ảnh")
        print(f"   Tổng thời gian predict: {total_time:.2f}s")
        
        # Đánh giá
        print("\n🏆 ĐÁNH GIÁ:")
        if accuracy >= 95:
            print("   🎉 XUẤT SẮC! Model hoạt động rất tốt!")
        elif accuracy >= 85:
            print("   ✅ TỐT! Model hoạt động ổn định!")
        elif accuracy >= 70:
            print("   ⚠️ TRUNG BÌNH! Cần cải thiện!")
        else:
            print("   ❌ KÉM! Cần kiểm tra lại!")
            
        if avg_time <= 0.2:
            print("   ⚡ Tốc độ RẤT NHANH!")
        elif avg_time <= 0.5:
            print("   🚀 Tốc độ NHANH!")
        elif avg_time <= 1.0:
            print("   ⏱️ Tốc độ CHẤP NHẬN ĐƯỢC!")
        else:
            print("   🐌 Tốc độ CHẬM!")
            
        print("\n✅ TEST CUỐI CÙNG HOÀN THÀNH!")
        print("🎯 Keras model đã sẵn sàng sử dụng trong production!")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_keras()
