#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra tích hợp <PERSON> model vào ứng dụng chính
"""

import os
import sys
import time

# Thêm thư mục gốc vào path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)

def test_keras_integration():
    """Test tích hợp Keras model"""
    
    print("🔄 Test tích hợp Keras model...")
    
    try:
        # Import các function từ file chính
        from kiem_tra_mst_qt import load_model, solve_captcha, preprocess_image_for_keras, decode_ctc_predictions
        
        print("✅ Import thành công")
        
        # Test load model
        print("🔄 Đang load model...")
        start_time = time.time()
        
        def progress_callback(percent):
            print(f"   Progress: {percent}%")
        
        load_model(progress_callback)
        load_time = time.time() - start_time
        print(f"✅ Load model thành công trong {load_time:.2f}s")
        
        # Test với ảnh captcha thật nếu có
        train_data_dir = os.path.join(BASE_DIR, "train_data")
        if os.path.exists(train_data_dir):
            # Lấy một vài ảnh để test
            image_files = [f for f in os.listdir(train_data_dir) if f.endswith('.png')][:5]
            
            if image_files:
                print(f"\n🖼️ Test với {len(image_files)} ảnh thật:")
                
                for i, image_file in enumerate(image_files, 1):
                    image_path = os.path.join(train_data_dir, image_file)
                    expected_label = os.path.splitext(image_file)[0]
                    
                    print(f"\n{i}. Test ảnh: {image_file}")
                    print(f"   Expected: {expected_label}")
                    
                    # Test solve_captcha
                    start_time = time.time()
                    result = solve_captcha(image_path)
                    solve_time = time.time() - start_time
                    
                    print(f"   Predicted: {result}")
                    print(f"   Time: {solve_time:.3f}s")
                    
                    # So sánh kết quả
                    if result == expected_label:
                        print("   ✅ ĐÚNG!")
                    else:
                        print("   ❌ SAI!")
                        
                        # Test từng bước để debug
                        print("   🔍 Debug:")
                        try:
                            # Test preprocess
                            img_array = preprocess_image_for_keras(image_path)
                            print(f"      Preprocessed shape: {img_array.shape}")
                            
                            # Test model predict
                            import tensorflow as tf
                            from kiem_tra_mst_qt import _model, _device
                            
                            if _model is not None:
                                with tf.device(_device):
                                    predictions = _model.predict(img_array, verbose=0)
                                print(f"      Predictions shape: {predictions.shape}")
                                
                                # Test decode
                                decoded = decode_ctc_predictions(predictions)
                                print(f"      Decoded: {decoded}")
                            else:
                                print("      ❌ Model chưa được load")
                                
                        except Exception as debug_e:
                            print(f"      ❌ Debug error: {debug_e}")
            else:
                print("❌ Không tìm thấy ảnh test trong train_data")
        else:
            print("❌ Không tìm thấy thư mục train_data")
            
            # Tạo ảnh test giả
            print("\n🖼️ Test với ảnh giả:")
            import numpy as np
            from PIL import Image
            
            # Tạo ảnh test 200x50 grayscale
            test_image = np.random.randint(0, 256, (50, 200), dtype=np.uint8)
            pil_image = Image.fromarray(test_image, mode='L')
            
            result = solve_captcha(pil_image)
            print(f"   Kết quả với ảnh giả: {result}")
        
        print("\n✅ TEST HOÀN THÀNH!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Đảm bảo đã cài đặt TensorFlow: pip install tensorflow")
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_keras_integration()
