#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra Keras model trong folder Model
"""

import os
import sys
import numpy as np
from PIL import Image
import io

# Thêm thư mục gốc vào path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)

def test_keras_model():
    """Test Keras model với một ảnh captcha mẫu"""
    
    # Đường dẫn model
    model_path = os.path.join(BASE_DIR, "Model", "captcha_prediction_model.keras")
    
    if not os.path.exists(model_path):
        print(f"❌ Không tìm thấy model tại: {model_path}")
        return
    
    print(f"✅ Tìm thấy model tại: {model_path}")
    
    try:
        # Import TensorFlow/Keras
        import tensorflow as tf
        from tensorflow import keras
        print(f"✅ TensorFlow version: {tf.__version__}")
        
        # Load model
        print("🔄 Đang tải model...")
        model = keras.models.load_model(model_path)
        print("✅ Đã tải model thành công")
        
        # In thông tin model
        print("\n📊 Thông tin model:")
        model.summary()
        
        # Kiểm tra input shape
        input_shape = model.input_shape
        print(f"\n📐 Input shape: {input_shape}")
        
        # Tạo ảnh test giả
        if len(input_shape) == 4:  # (batch, height, width, channels)
            height, width, channels = input_shape[1], input_shape[2], input_shape[3]
        else:
            print(f"❌ Unexpected input shape: {input_shape}")
            return
            
        print(f"📏 Expected image size: {width}x{height}x{channels}")
        
        # Tạo ảnh test ngẫu nhiên
        test_image = np.random.randint(0, 256, (height, width, channels), dtype=np.uint8)
        test_image_batch = np.expand_dims(test_image, axis=0)  # Thêm batch dimension
        
        print(f"🖼️ Test image shape: {test_image_batch.shape}")
        
        # Predict
        print("🔄 Đang predict...")
        predictions = model.predict(test_image_batch)
        print(f"✅ Prediction shape: {predictions.shape}")
        print(f"📊 Predictions: {predictions}")
        
        # Nếu là classification, tìm class có xác suất cao nhất
        if len(predictions.shape) == 2:  # (batch, classes)
            predicted_class = np.argmax(predictions[0])
            confidence = predictions[0][predicted_class]
            print(f"🎯 Predicted class: {predicted_class} (confidence: {confidence:.4f})")
        
        # Test với ảnh captcha thật nếu có
        train_data_dir = os.path.join(BASE_DIR, "train_data")
        if os.path.exists(train_data_dir):
            # Lấy ảnh đầu tiên trong train_data
            image_files = [f for f in os.listdir(train_data_dir) if f.endswith('.png')]
            if image_files:
                test_image_path = os.path.join(train_data_dir, image_files[0])
                print(f"\n🖼️ Test với ảnh thật: {image_files[0]}")
                
                # Load và resize ảnh
                img = Image.open(test_image_path)
                if channels == 1:
                    img = img.convert('L')  # Grayscale
                elif channels == 3:
                    img = img.convert('RGB')
                elif channels == 4:
                    img = img.convert('RGBA')
                
                # Resize về kích thước model mong đợi
                img_resized = img.resize((width, height))
                img_array = np.array(img_resized)
                
                # Normalize nếu cần
                if img_array.max() > 1:
                    img_array = img_array.astype(np.float32) / 255.0
                
                # Thêm batch dimension
                img_batch = np.expand_dims(img_array, axis=0)
                
                print(f"📏 Real image shape: {img_batch.shape}")
                
                # Predict
                real_predictions = model.predict(img_batch)
                print(f"📊 Real image predictions: {real_predictions}")
                
                if len(real_predictions.shape) == 2:
                    predicted_class = np.argmax(real_predictions[0])
                    confidence = real_predictions[0][predicted_class]
                    print(f"🎯 Real image predicted class: {predicted_class} (confidence: {confidence:.4f})")
                    
                    # Nếu có label trong tên file
                    filename_without_ext = os.path.splitext(image_files[0])[0]
                    print(f"📝 Expected label from filename: {filename_without_ext}")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Cần cài đặt TensorFlow: pip install tensorflow")
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_keras_model()
