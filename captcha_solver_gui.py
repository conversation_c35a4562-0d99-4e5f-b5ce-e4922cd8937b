"""
Captcha Solver GUI Tool
Version: 1.0
Author: AI Assistant
Date: 2025-01-28
Description: GUI tool for solving captcha images using trained models
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
from pathlib import Path
import numpy as np
import tensorflow as tf
import keras
from keras import ops
from keras import layers
from PIL import Image, ImageTk
import glob
from datetime import datetime

class CaptchaSolverGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Captcha Solver Tool v1.0")
        self.root.geometry("1000x700")
        
        # Variables
        self.selected_model_path = tk.StringVar()
        self.selected_files = []
        self.selected_folders = []
        self.model = None
        self.char_to_num = None
        self.num_to_char = None
        self.is_processing = False
        
        # Image dimensions (same as training)
        self.img_width = 200
        self.img_height = 50
        
        self.setup_ui()
        self.load_available_models()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Model selection section
        model_frame = ttk.LabelFrame(main_frame, text="Model Selection", padding="10")
        model_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        model_frame.columnconfigure(1, weight=1)
        
        ttk.Label(model_frame, text="Select Model:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.model_combo = ttk.Combobox(model_frame, textvariable=self.selected_model_path, 
                                       state="readonly", width=50)
        self.model_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(model_frame, text="Browse Model", 
                  command=self.browse_model).grid(row=0, column=2, sticky=tk.W)
        
        ttk.Button(model_frame, text="Load Model", 
                  command=self.load_model).grid(row=0, column=3, sticky=tk.W, padx=(10, 0))
        
        # Model status
        self.model_status = ttk.Label(model_frame, text="No model loaded", foreground="red")
        self.model_status.grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(5, 0))
        
        # File selection section
        file_frame = ttk.LabelFrame(main_frame, text="File Selection", padding="10")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(0, weight=1)
        
        # Buttons frame
        btn_frame = ttk.Frame(file_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(btn_frame, text="Add Files", 
                  command=self.add_files).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Add Folder", 
                  command=self.add_folder).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(btn_frame, text="Clear All", 
                  command=self.clear_selections).grid(row=0, column=2, padx=(0, 10))
        
        # Selected files list
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        self.file_listbox = tk.Listbox(list_frame, height=8)
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        # Processing section
        process_frame = ttk.LabelFrame(main_frame, text="Processing", padding="10")
        process_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(process_frame, text="Start Processing", 
                  command=self.start_processing).grid(row=0, column=0, padx=(0, 10))
        
        self.progress = ttk.Progressbar(process_frame, mode='determinate')
        self.progress.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        process_frame.columnconfigure(1, weight=1)
        
        self.progress_label = ttk.Label(process_frame, text="Ready")
        self.progress_label.grid(row=0, column=2)
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=15, width=80)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Export button
        ttk.Button(results_frame, text="Export Results", 
                  command=self.export_results).grid(row=1, column=0, pady=(10, 0))
        
    def load_available_models(self):
        """Load available models from current directory"""
        models = []
        
        # Look for model directories
        for item in os.listdir('.'):
            if os.path.isdir(item) and item.startswith('Model_'):
                model_file = os.path.join(item, 'captcha_prediction_model.keras')
                if os.path.exists(model_file):
                    models.append(model_file)
        
        self.model_combo['values'] = models
        if models:
            self.model_combo.set(models[0])  # Select first model by default
            
    def browse_model(self):
        """Browse for model file"""
        filename = filedialog.askopenfilename(
            title="Select Model File",
            filetypes=[("Keras Model", "*.keras"), ("All Files", "*.*")]
        )
        if filename:
            self.selected_model_path.set(filename)
            
    def load_model(self):
        """Load the selected model"""
        model_path = self.selected_model_path.get()
        if not model_path or not os.path.exists(model_path):
            messagebox.showerror("Error", "Please select a valid model file")
            return
            
        try:
            self.model = keras.models.load_model(model_path)
            
            # Setup character mappings (same as training)
            data_dir = Path("./captcha_images_v2/")
            if data_dir.exists():
                images = sorted(list(map(str, list(data_dir.glob("*.png")))))
                labels = [img.split(os.path.sep)[-1].split(".png")[0] for img in images]
                characters = set(char for label in labels for char in label)
                characters = sorted(list(characters))
                
                self.char_to_num = layers.StringLookup(vocabulary=list(characters), mask_token=None)
                self.num_to_char = layers.StringLookup(
                    vocabulary=self.char_to_num.get_vocabulary(), mask_token=None, invert=True
                )
                
                self.model_status.config(text=f"Model loaded successfully: {os.path.basename(model_path)}", 
                                       foreground="green")
            else:
                messagebox.showerror("Error", "Training data directory not found. Cannot setup character mappings.")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load model: {str(e)}")
            self.model_status.config(text="Failed to load model", foreground="red")
            
    def add_files(self):
        """Add individual files"""
        files = filedialog.askopenfilenames(
            title="Select Captcha Images",
            filetypes=[("Image Files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All Files", "*.*")]
        )
        
        for file in files:
            if file not in self.selected_files:
                self.selected_files.append(file)
                self.file_listbox.insert(tk.END, f"FILE: {os.path.basename(file)}")
                
    def add_folder(self):
        """Add folder containing images"""
        folder = filedialog.askdirectory(title="Select Folder with Captcha Images")
        if folder:
            if folder not in self.selected_folders:
                self.selected_folders.append(folder)
                self.file_listbox.insert(tk.END, f"FOLDER: {os.path.basename(folder)}")
                
    def clear_selections(self):
        """Clear all selected files and folders"""
        self.selected_files.clear()
        self.selected_folders.clear()
        self.file_listbox.delete(0, tk.END)
        
    def get_all_image_files(self):
        """Get all image files from selected files and folders"""
        all_files = []
        
        # Add individual files
        all_files.extend(self.selected_files)
        
        # Add files from folders
        for folder in self.selected_folders:
            for ext in ['*.png', '*.jpg', '*.jpeg', '*.gif', '*.bmp']:
                all_files.extend(glob.glob(os.path.join(folder, ext)))
                all_files.extend(glob.glob(os.path.join(folder, ext.upper())))
                
        return list(set(all_files))  # Remove duplicates
        
    def preprocess_image(self, img_path):
        """Preprocess image for model prediction"""
        try:
            # Read image
            img = tf.io.read_file(img_path)
            # Decode with alpha channel support
            img = tf.io.decode_png(img, channels=4)  # RGBA
            
            # Convert transparent background to white
            rgb = img[:, :, :3]
            alpha = img[:, :, 3:4]
            
            rgb = tf.image.convert_image_dtype(rgb, tf.float32)
            alpha = tf.image.convert_image_dtype(alpha, tf.float32)
            
            white_bg = tf.ones_like(rgb)
            img_blended = alpha * rgb + (1 - alpha) * white_bg
            
            # Convert to grayscale
            img = tf.image.rgb_to_grayscale(img_blended)
            
            # Resize
            img = ops.image.resize(img, [self.img_height, self.img_width])
            
            # Transpose
            img = ops.transpose(img, axes=[1, 0, 2])
            
            return img
            
        except Exception as e:
            print(f"Error preprocessing {img_path}: {str(e)}")
            return None

    def ctc_decode(self, y_pred, input_length, greedy=True, beam_width=100, top_paths=1):
        """CTC decode function"""
        input_shape = ops.shape(y_pred)
        num_samples, num_steps = input_shape[0], input_shape[1]
        y_pred = ops.log(ops.transpose(y_pred, axes=[1, 0, 2]) + keras.backend.epsilon())
        input_length = ops.cast(input_length, dtype="int32")

        if greedy:
            (decoded, log_prob) = tf.nn.ctc_greedy_decoder(
                inputs=y_pred, sequence_length=input_length
            )
        else:
            (decoded, log_prob) = tf.compat.v1.nn.ctc_beam_search_decoder(
                inputs=y_pred,
                sequence_length=input_length,
                beam_width=beam_width,
                top_paths=top_paths,
            )
        decoded_dense = []
        for st in decoded:
            st = tf.SparseTensor(st.indices, st.values, (num_samples, num_steps))
            decoded_dense.append(tf.sparse.to_dense(sp_input=st, default_value=-1))
        return (decoded_dense, log_prob)

    def decode_batch_predictions(self, pred):
        """Decode model predictions to text"""
        input_len = np.ones(pred.shape[0]) * pred.shape[1]
        max_length = 5  # Maximum captcha length

        results = self.ctc_decode(pred, input_length=input_len, greedy=True)[0][0][:, :max_length]

        output_text = []
        for res in results:
            res = tf.strings.reduce_join(self.num_to_char(res)).numpy().decode("utf-8")
            output_text.append(res)
        return output_text

    def predict_single_image(self, img_path):
        """Predict text for a single image"""
        try:
            img = self.preprocess_image(img_path)
            if img is None:
                return None

            # Add batch dimension
            img_batch = tf.expand_dims(img, 0)

            # Predict
            pred = self.model.predict(img_batch, verbose=0)

            # Decode
            pred_text = self.decode_batch_predictions(pred)[0]

            return pred_text

        except Exception as e:
            print(f"Error predicting {img_path}: {str(e)}")
            return None

    def process_images_thread(self):
        """Process images in a separate thread"""
        try:
            all_files = self.get_all_image_files()

            if not all_files:
                self.root.after(0, lambda: messagebox.showwarning("Warning", "No image files selected"))
                return

            if not self.model:
                self.root.after(0, lambda: messagebox.showerror("Error", "No model loaded"))
                return

            total_files = len(all_files)
            self.root.after(0, lambda: self.progress.config(maximum=total_files))

            results = []
            successful = 0
            failed = 0

            for i, img_path in enumerate(all_files):
                if not self.is_processing:  # Check if processing was cancelled
                    break

                # Update progress
                self.root.after(0, lambda i=i: self.progress.config(value=i))
                self.root.after(0, lambda i=i, total=total_files:
                              self.progress_label.config(text=f"Processing {i+1}/{total}"))

                # Predict
                prediction = self.predict_single_image(img_path)

                if prediction:
                    results.append(f"✓ {os.path.basename(img_path)}: {prediction}")
                    successful += 1
                else:
                    results.append(f"✗ {os.path.basename(img_path)}: FAILED")
                    failed += 1

            # Update UI with results
            self.root.after(0, lambda: self.update_results(results, successful, failed, total_files))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Processing failed: {str(e)}"))
        finally:
            self.is_processing = False
            self.root.after(0, lambda: self.progress_label.config(text="Completed"))

    def update_results(self, results, successful, failed, total):
        """Update results display"""
        self.results_text.delete(1.0, tk.END)

        # Add summary
        summary = f"=== PROCESSING SUMMARY ===\n"
        summary += f"Total files: {total}\n"
        summary += f"Successful: {successful}\n"
        summary += f"Failed: {failed}\n"
        summary += f"Success rate: {(successful/total*100):.1f}%\n\n"
        summary += f"=== DETAILED RESULTS ===\n"

        self.results_text.insert(tk.END, summary)

        # Add individual results
        for result in results:
            self.results_text.insert(tk.END, result + "\n")

        # Scroll to top
        self.results_text.see(1.0)

    def start_processing(self):
        """Start processing images"""
        if self.is_processing:
            messagebox.showwarning("Warning", "Processing is already in progress")
            return

        if not self.model:
            messagebox.showerror("Error", "Please load a model first")
            return

        all_files = self.get_all_image_files()
        if not all_files:
            messagebox.showwarning("Warning", "Please select some image files or folders")
            return

        self.is_processing = True
        self.progress.config(value=0)
        self.progress_label.config(text="Starting...")

        # Start processing in a separate thread
        thread = threading.Thread(target=self.process_images_thread)
        thread.daemon = True
        thread.start()

    def export_results(self):
        """Export results to a text file"""
        content = self.results_text.get(1.0, tk.END).strip()
        if not content:
            messagebox.showwarning("Warning", "No results to export")
            return

        filename = filedialog.asksaveasfilename(
            title="Save Results",
            defaultextension=".txt",
            filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")],
            initialname=f"captcha_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("Success", f"Results exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export results: {str(e)}")

def main():
    root = tk.Tk()
    app = CaptchaSolverGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
