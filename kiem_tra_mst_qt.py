
# Để chạy file này, bạn cần cài đặt PySide6:
# pip install PySide6
#
# File này là phiên bản đư<PERSON><PERSON> viết lại của kiem_tra_mst.py, sử dụng bộ công cụ Qt (thông qua PySide6)
# để có giao diện hiện đại hơn và hỗ trợ tốt hơn cho emoji màu.

import sys, os, datetime, threading, re, json, configparser, traceback, concurrent.futures, time, requests, platform, subprocess, io
from typing import Optional, List, Dict, Any, Tuple
import random
import numpy as np
from PIL import Image, ImageTk, ImageDraw, ImageFont

# --- Thêm import cho PySide6 ---
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QTextEdit, QLineEdit, QPushButton, QProgressBar,
    QMessageBox, QDialog, QFileDialog, QCheckBox, QFrame
)
from PySide6.QtCore import (
    Qt, QRunnable, QThreadPool, QObject, Signal, QTimer, QSize, Slot
)
from PySide6.QtGui import QFont, QMovie, QPixmap, QTransform, QIcon

# ==============================================================================
# HARDWARE DETECTOR CLASS
# ==============================================================================

class HardwareDetector:
    """Class để detect và quản lý thông tin phần cứng"""

    def __init__(self):
        self._detection_cache = {}
        self._lock = threading.Lock()
        self._best_device_cache = None

    def get_cpu_info(self) -> Dict[str, str]:
        """Lấy thông tin CPU"""
        if 'cpu' in self._detection_cache:
            return self._detection_cache['cpu']

        cpu_info = {
            'name': platform.processor() or 'Unknown CPU',
            'architecture': platform.machine(),
            'cores': str(os.cpu_count() or 'Unknown')
        }

        # Thử lấy thông tin chi tiết hơn trên Windows
        if platform.system() == 'Windows':
            try:
                result = subprocess.run(['wmic', 'cpu', 'get', 'name'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    if len(lines) > 1:
                        cpu_name = lines[1].strip()
                        if cpu_name:
                            cpu_info['name'] = cpu_name
            except:
                pass

        self._detection_cache['cpu'] = cpu_info
        return cpu_info

    def get_nvidia_gpu_info(self) -> List[Dict[str, str]]:
        """Lấy thông tin GPU NVIDIA qua nvidia-smi"""
        if 'nvidia' in self._detection_cache:
            return self._detection_cache['nvidia']

        gpus = []
        try:
            # Thử chạy nvidia-smi
            result = subprocess.run([
                'nvidia-smi',
                '--query-gpu=name,memory.total,driver_version,compute_cap',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for i, line in enumerate(lines):
                    if line.strip():
                        parts = [p.strip() for p in line.split(',')]
                        if len(parts) >= 4:
                            gpus.append({
                                'index': str(i),
                                'name': parts[0],
                                'memory': f"{parts[1]} MB",
                                'driver': parts[2],
                                'compute_cap': parts[3],
                                'vendor': 'NVIDIA'
                            })
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            pass

        self._detection_cache['nvidia'] = gpus
        return gpus

    def get_intel_gpu_info(self) -> List[Dict[str, str]]:
        """Lấy thông tin GPU Intel"""
        if 'intel' in self._detection_cache:
            return self._detection_cache['intel']

        gpus = []

        if platform.system() == 'Windows':
            try:
                result = subprocess.run([
                    'wmic', 'path', 'win32_VideoController', 'get', 'name,AdapterRAM'
                ], capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for i, line in enumerate(lines[1:]):  # Skip header
                        line = line.strip()
                        if line and ('Intel' in line or 'UHD' in line or 'Iris' in line or 'HD Graphics' in line):
                            parts = line.split()
                            name = ' '.join(parts[:-1]) if parts and parts[-1].isdigit() else line
                            memory = parts[-1] if parts and parts[-1].isdigit() else 'Unknown'

                            gpus.append({
                                'index': str(i),
                                'name': name,
                                'memory': memory,
                                'vendor': 'Intel',
                                'type': 'integrated'
                            })
            except:
                pass

        self._detection_cache['intel'] = gpus
        return gpus

    def get_pytorch_device_info(self) -> Dict[str, any]:
        """Lấy thông tin device từ PyTorch"""
        if 'pytorch' in self._detection_cache:
            return self._detection_cache['pytorch']

        device_info = {
            'torch_available': False,
            'cuda_available': False,
            'cuda_device_count': 0,
            'cuda_devices': [],
            'current_device': 'cpu'
        }

        try:
            import torch
            device_info['torch_available'] = True

            # CUDA info
            if torch.cuda.is_available():
                device_info['cuda_available'] = True
                device_info['cuda_device_count'] = torch.cuda.device_count()
                device_info['current_device'] = 'cuda'

                for i in range(torch.cuda.device_count()):
                    props = torch.cuda.get_device_properties(i)
                    device_info['cuda_devices'].append({
                        'index': i,
                        'name': props.name,
                        'memory': f"{props.total_memory // (1024**2)} MB",
                        'compute_capability': f"{props.major}.{props.minor}",
                        'multiprocessor_count': props.multi_processor_count
                    })

            # Thử các backend khác
            backends = []

            # Intel XPU (Intel GPU)
            try:
                if hasattr(torch, 'xpu') and hasattr(torch.xpu, 'is_available') and torch.xpu.is_available():
                    xpu_count = torch.xpu.device_count() if hasattr(torch.xpu, 'device_count') else 1
                    if xpu_count > 0:
                        # Thử lấy thông tin chi tiết Intel GPU
                        try:
                            xpu_name = torch.xpu.get_device_name(0) if hasattr(torch.xpu, 'get_device_name') else 'Intel GPU'
                            backends.append(f'XPU (Intel {xpu_name})')
                        except:
                            backends.append('XPU (Intel GPU)')

                        # Lưu thông tin Intel GPU
                        device_info['xpu_available'] = True
                        device_info['xpu_device_count'] = xpu_count
            except:
                pass

            # Apple MPS
            try:
                if hasattr(torch, 'mps') and hasattr(torch.mps, 'is_available') and torch.mps.is_available():
                    backends.append('MPS (Apple)')
            except:
                pass

            device_info['other_backends'] = backends

        except ImportError:
            pass

        self._detection_cache['pytorch'] = device_info
        return device_info

    def determine_best_device(self) -> Tuple[str, str, Dict]:
        """Xác định device tốt nhất cho OCR"""
        if self._best_device_cache:
            return self._best_device_cache

        with self._lock:
            if self._best_device_cache:
                return self._best_device_cache

            pytorch_info = self.get_pytorch_device_info()

            # Ưu tiên CUDA nếu có (NVIDIA GPU)
            if pytorch_info['cuda_available'] and pytorch_info['cuda_device_count'] > 0:
                best_cuda = None
                max_memory = 0

                for device in pytorch_info['cuda_devices']:
                    memory_mb = int(device['memory'].split()[0])
                    if memory_mb > max_memory:
                        max_memory = memory_mb
                        best_cuda = device

                if best_cuda:
                    result = ('cuda', f"NVIDIA {best_cuda['name']}", best_cuda)
                    self._best_device_cache = result
                    return result

            # Ưu tiên Intel XPU nếu có
            if pytorch_info.get('xpu_available', False):
                xpu_info = {
                    'device_count': pytorch_info.get('xpu_device_count', 1),
                    'vendor': 'Intel'
                }
                # Tìm tên Intel GPU từ backends
                intel_name = 'Intel GPU'
                for backend in pytorch_info.get('other_backends', []):
                    if 'Intel' in backend:
                        intel_name = backend.replace('XPU (', '').replace(')', '')
                        break

                result = ('xpu', intel_name, xpu_info)
                self._best_device_cache = result
                return result

            # Thử các backend khác (MPS, etc.)
            other_backends = pytorch_info.get('other_backends', [])
            if other_backends:
                backend = other_backends[0]
                backend_type = backend.split()[0].lower()
                result = (backend_type, backend, {})
                self._best_device_cache = result
                return result

            # Fallback to CPU, nhưng kiểm tra Intel GPU integrated
            cpu_info = self.get_cpu_info()
            intel_gpus = self.get_intel_gpu_info()

            # Nếu có Intel GPU integrated, ghi chú trong thông tin CPU
            if intel_gpus:
                intel_gpu = intel_gpus[0]  # Lấy GPU đầu tiên
                cpu_info['intel_gpu'] = intel_gpu['name']
                cpu_info['gpu_type'] = 'integrated'
                result = ('cpu', f"CPU: {cpu_info['name']} + {intel_gpu['name']}", cpu_info)
            else:
                result = ('cpu', f"CPU: {cpu_info['name']}", cpu_info)

            self._best_device_cache = result
            return result

    def get_device_display_name(self) -> str:
        """Lấy tên hiển thị của device đang dùng cho OCR"""
        device_type, device_name, device_info = self.determine_best_device()

        if device_type == 'cuda':
            # Rút gọn tên NVIDIA GPU
            name = device_name.replace('NVIDIA ', '')
            if 'memory' in device_info:
                memory = device_info['memory'].replace(' MB', 'MB')
                return f"🎮 {name} ({memory})"
            return f"🎮 {name}"
        elif device_type == 'xpu':
            # Intel GPU
            name = device_name.replace('Intel ', '')
            device_count = device_info.get('device_count', 1)
            if device_count > 1:
                return f"🔷 {name} (x{device_count})"
            return f"🔷 {name}"
        elif device_type == 'mps':
            # Apple GPU
            return f"🍎 Apple GPU (MPS)"
        elif device_type == 'cpu':
            cpu_info = self.get_cpu_info()
            cores = cpu_info.get('cores', 'Unknown')

            # Kiểm tra có Intel GPU integrated không
            if 'intel_gpu' in device_info:
                intel_gpu_name = device_info['intel_gpu']
                # Rút gọn tên Intel GPU
                if 'Intel' in intel_gpu_name:
                    intel_gpu_name = intel_gpu_name.replace('Intel(R) ', '').replace('Intel ', '')
                return f"💻 CPU ({cores} cores) + 🔷 {intel_gpu_name}"

            # Rút gọn tên CPU
            cpu_name = cpu_info['name']
            if 'Intel' in cpu_name:
                # Tìm model number
                parts = cpu_name.split()
                for part in parts:
                    if any(char.isdigit() for char in part) and len(part) > 2:
                        return f"💻 Intel CPU ({cores} cores)"
                return f"💻 Intel CPU ({cores} cores)"
            elif 'AMD' in cpu_name:
                return f"💻 AMD CPU ({cores} cores)"
            else:
                return f"💻 CPU ({cores} cores)"
        else:
            return f"🔧 {device_name}"

    def get_detailed_info(self) -> Dict[str, any]:
        """Lấy thông tin chi tiết về tất cả phần cứng"""
        return {
            'cpu': self.get_cpu_info(),
            'nvidia_gpus': self.get_nvidia_gpu_info(),
            'intel_gpus': self.get_intel_gpu_info(),
            'pytorch': self.get_pytorch_device_info(),
            'best_device': self.determine_best_device()
        }

    def clear_cache(self):
        """Xóa cache để detect lại"""
        with self._lock:
            self._detection_cache.clear()
            self._best_device_cache = None

# Global instance
hardware_detector = HardwareDetector()

# ==============================================================================
# PHẦN CẤU HÌNH VÀ LOGIC BACKEND (GIỮ NGUYÊN TỪ BẢN GỐC)
# ==============================================================================

cfg = configparser.ConfigParser()
cfg.read("setting.ini", encoding="utf-8")
default_workers = cfg.getint("DEFAULT", "max_workers", fallback=20)
default_retry_failed = cfg.getint("DEFAULT", "retry_failed", fallback=20)
default_timeout = cfg.getint("DEFAULT", "request_timeout", fallback=120)
default_ocr_timeout = cfg.getint("DEFAULT", "ocr_timeout", fallback=120)

# ======= BẮT ĐẦU PHẦN GỘP TỪ keras_solver.py =======
# Đường dẫn thư mục chứa model đã tải sẵn
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
MODEL_DIR = os.path.join(BASE_DIR, "Model")
KERAS_MODEL_PATH = os.path.join(MODEL_DIR, "captcha_prediction_model.keras")

_model = None
_device = None
_model_lock = threading.Lock()
_model_loaded = False

# Character mapping cho CTC model
CHARACTERS = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ"  # 34 characters + blank = 35 classes
CHAR_TO_NUM = {char: i for i, char in enumerate(CHARACTERS)}
NUM_TO_CHAR = {i: char for i, char in enumerate(CHARACTERS)}

def load_model(progress_callback=None):
    """
    Tải Keras model để giải captcha.
    Có thể gọi nhiều lần, chỉ load 1 lần (thread-safe).
    Sử dụng hardware detector để chọn device tốt nhất.
    """
    global _model, _device, _model_loaded
    if _model_loaded:
        if progress_callback: progress_callback(100)
        return
    with _model_lock:
        if _model_loaded:
            if progress_callback: progress_callback(100)
            return
        if progress_callback: progress_callback(5)

        try:
            import tensorflow as tf

            # Sử dụng hardware detector để chọn device tốt nhất
            device_type, device_name, device_info = hardware_detector.determine_best_device()

            # Cấu hình TensorFlow device
            if device_type == 'cuda':
                # Kiểm tra GPU có sẵn không
                gpus = tf.config.experimental.list_physical_devices('GPU')
                if gpus:
                    try:
                        # Cho phép memory growth
                        for gpu in gpus:
                            tf.config.experimental.set_memory_growth(gpu, True)
                        _device = '/GPU:0'
                    except RuntimeError:
                        _device = '/CPU:0'
                else:
                    _device = '/CPU:0'
            else:
                _device = '/CPU:0'

            if progress_callback: progress_callback(30)

            # Load Keras model
            with tf.device(_device):
                _model = tf.keras.models.load_model(KERAS_MODEL_PATH)

            if progress_callback: progress_callback(90)
            _model_loaded = True
            if progress_callback: progress_callback(100)

        except Exception as e:
            raise Exception(f"Không thể tải Keras model: {e}")

def preprocess_image(image_path_or_buffer):
    image = Image.open(image_path_or_buffer).convert("RGBA")
    background = Image.new("RGBA", image.size, (255, 255, 255, 255))
    combined = Image.alpha_composite(background, image).convert("RGB")
    return combined

def preprocess_image_for_keras(image_path_or_buffer):
    """Xử lý ảnh cho Keras model"""
    try:
        if isinstance(image_path_or_buffer, (str, bytes)):
            if isinstance(image_path_or_buffer, str):
                # Đường dẫn file
                image = Image.open(image_path_or_buffer)
            else:
                # Bytes buffer
                image = Image.open(io.BytesIO(image_path_or_buffer))
        else:
            # Giả sử là PIL Image
            image = image_path_or_buffer

        # Xử lý RGBA images - tạo background trắng trước khi convert
        if image.mode == 'RGBA':
            # Tạo background trắng
            background = Image.new('RGBA', image.size, (255, 255, 255, 255))
            # Composite image lên background
            image = Image.alpha_composite(background, image)

        # Chuyển về RGB trước, sau đó mới về grayscale
        image = image.convert('RGB').convert('L')

        # Resize về kích thước model mong đợi (50x200) - width x height
        # Model expects (200, 50, 1) so we need width=50, height=200
        image = image.resize((50, 200), Image.Resampling.LANCZOS)

        # Chuyển thành numpy array
        img_array = np.array(image, dtype=np.float32)

        # Normalize về [0, 1]
        img_array = img_array / 255.0

        # Thêm channel dimension (200, 50) -> (200, 50, 1)
        img_array = np.expand_dims(img_array, axis=-1)

        # Thêm batch dimension (200, 50, 1) -> (1, 200, 50, 1)
        img_array = np.expand_dims(img_array, axis=0)

        return img_array
    except Exception as e:
        raise Exception(f"Lỗi xử lý ảnh: {e}")

def decode_ctc_predictions(predictions):
    """Giải mã CTC predictions thành text"""
    try:
        # predictions shape: (1, 50, 25)
        # Lấy class có xác suất cao nhất cho mỗi time step
        predicted_classes = np.argmax(predictions[0], axis=1)  # (50,)

        # CTC decoding: loại bỏ blank (class 24) và duplicate
        decoded_chars = []
        prev_class = -1

        for class_idx in predicted_classes:
            # Class 24 là blank token, bỏ qua
            if class_idx == 24:  # blank token
                prev_class = class_idx
                continue

            # Bỏ qua duplicate liên tiếp
            if class_idx != prev_class:
                if class_idx < len(CHARACTERS):
                    decoded_chars.append(CHARACTERS[class_idx])

            prev_class = class_idx

        return ''.join(decoded_chars)
    except Exception as e:
        return f"❌ Lỗi decode: {e}"

def solve_captcha(image_path_or_buffer):
    global _model, _device, _model_loaded
    if not _model_loaded or _model is None:
        load_model()
    try:
        import tensorflow as tf

        # Xử lý ảnh
        img_array = preprocess_image_for_keras(image_path_or_buffer)

        # Predict với TensorFlow device context
        with tf.device(_device):
            predictions = _model.predict(img_array, verbose=0)

        # Giải mã kết quả
        text = decode_ctc_predictions(predictions)
        return text.strip()
    except Exception as e:
        return f"❌ Lỗi OCR: {e}"
# ======= KẾT THÚC PHẦN GỘP TỪ trocr_solver.py =======


# ==============================================================================
# PHẦN GIAO DIỆN (UI) VÀ LOGIC TƯƠNG TÁC VỚI QT
# ==============================================================================

class WorkerSignals(QObject):
    """
    Định nghĩa các tín hiệu (signals) có thể được phát ra từ một worker thread.
    Bao gồm các tín hiệu cho việc log, cập nhật dòng kết quả, báo cáo kết quả captcha,
    và báo hiệu khi worker hoàn thành.
    """
    log = Signal(str)
    update_result = Signal(int, str)
    captcha_result = Signal(bool) # True for OK, False for Fail
    task_completed = Signal(bool) # True nếu vòng lặp retry thành công, False nếu thất bại
    finished = Signal()
    error = Signal(str)

class TraCuuWorker(QRunnable):
    """
    Worker class để tra cứu MST trong một thread riêng biệt sử dụng QThreadPool.
    """
    def __init__(self, mst: str, idx: int, debug_flags: dict):
        super().__init__()
        self.mst = mst
        self.idx = idx
        self.signals = WorkerSignals()
        self.session = None
        self.debug_flags = debug_flags

    @Slot()
    def run(self):
        self.session = requests.Session()
        self.signals.log.emit(f"[{self.mst}] 🚀 Bắt đầu xử lý")
        self.signals.update_result.emit(self.idx, f"Đang kiểm tra...\t{self.mst}")

        try:
            attempt = 0
            while attempt < default_retry_failed:
                try:
                    self.signals.log.emit(f"[{self.mst}] 🧩 Đang lấy captcha")
                    dia_chi = self.tra_cuu()
                    self.signals.log.emit(f"[{self.mst}] ✅ Đã lấy được địa chỉ")

                    if dia_chi.lower().startswith("không lấy được"):
                        raise Exception("Không lấy được địa chỉ")

                    if not dia_chi.strip():
                        dia_chi = "(Rỗng)"

                    result_text = f"{dia_chi}\t{self.mst}"
                    self.signals.update_result.emit(self.idx, result_text)
                    self.signals.task_completed.emit(True) # Báo hiệu thành công
                    break
                except Exception as e:
                    attempt += 1
                    error_msg = str(e)
                    self.signals.log.emit(f"[{self.mst}] ❌ Lỗi lần {attempt}: {error_msg}")

                    if attempt >= default_retry_failed:
                        fail_text = f"không lấy được địa chỉ\t{self.mst}"
                        self.signals.update_result.emit(self.idx, fail_text)
                        self.signals.task_completed.emit(False) # Báo hiệu thất bại

        except Exception as e:
            self.signals.error.emit(f"Lỗi nghiêm trọng worker [{self.mst}]: {e}\n{traceback.format_exc()}")
        finally:
            self.session.close()
            self.signals.finished.emit()


    def tra_cuu(self) -> str:
        if self.debug_flags.get('always_fail'):
            return "không lấy được địa chỉ (debug)"
        headers = {
            'User-Agent': 'Mozilla/5.0',
            'Referer': 'https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp'
        }

        try:
            self.session.get("https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp", headers=headers, timeout=default_timeout)
        except requests.RequestException as e:
            raise Exception(f"Lỗi kết nối: {e}")

        captcha_url = "https://tracuunnt.gdt.gov.vn/tcnnt/captcha.png"
        try:
            r = self.session.get(captcha_url, headers=headers, timeout=default_timeout)
            r.raise_for_status()
            image_buffer = io.BytesIO(r.content)
        except requests.RequestException as e:
            raise Exception(f"Lỗi tải captcha: {e}")

        if self.debug_flags.get('wrong_captcha'):
            code = ''.join(random.choices('ABCDEFGHJKLMNPQRSTUVWXYZ23456789', k=5))
        else:
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(solve_captcha, image_buffer)
                    code = future.result(timeout=default_ocr_timeout)
                    if not code or "❌" in code:
                        raise Exception("Lỗi OCR captcha")
            except concurrent.futures.TimeoutError:
                raise Exception(f"OCR captcha timeout sau {default_ocr_timeout} giây")
            except Exception as e:
                raise Exception(f"Lỗi giải captcha: {e}")

        data = {"mst": self.mst, "fullname": "", "address": "", "captcha": code}
        self.signals.log.emit(f"[{self.mst}] 📤 Gửi request tra cứu")

        try:
            res = self.session.post("https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp", data=data, headers=headers, timeout=default_timeout)
            res.raise_for_status()
        except requests.RequestException as e:
            raise Exception(f"Lỗi gửi request: {e}")

        html = res.text
        if self.debug_flags.get('save_response'):
            os.makedirs("debug", exist_ok=True)
            ts = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = os.path.join("debug", f"debug_raw_{self.mst}_{ts}.txt")
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(html)
            self.signals.log.emit(f"[DEBUG] Đã lưu file trả về: {file_path}")
            self.debug_flags['save_response'] = False # Chỉ lưu 1 lần

        if any(x in html.lower() for x in ["vui lòng nhập đúng mã xác nhận", "vui l&#242;ng nh&#7853;p &#273;&#250;ng m&#227; x&#225;c nh&#7853;n"]):
            self.signals.captcha_result.emit(False)
            raise Exception("Captcha sai")

        self.signals.captcha_result.emit(True)

        if "không tìm thấy người nộp thuế" in html.lower():
            return "Không tìm thấy người nộp thuế nào phù hợp."
        if "mã số thuế không hợp lệ" in html.lower():
            return "Mã số thuế không hợp lệ."

        try:
            match = re.search(r"var\s+nntJson\s*=\s*(\{.*?\});", html, re.DOTALL)
            if match:
                json_str = match.group(1)
                data = json.loads(json_str)
                info = next((item for item in data.get("DATA", []) if item.get("MST") == self.mst), None)
                if not info:
                    return "không lấy được địa chỉ (không tìm thấy MST phù hợp)"

                dkt_dia_chi = info.get("DKT_DIA_CHI", [])
                dia_chi_parts = {item.get("LOAI"): item for item in dkt_dia_chi}
                dia_chi_info = dia_chi_parts.get("0300", {})

                dia_chi = dia_chi_info.get("DIA_CHI", "")
                xa = dia_chi_info.get("PHUONG_XA", "")
                tinh = dia_chi_info.get("TINH_TP", "")

                if not dia_chi:
                    dia_chi = info.get("DIA_CHI_TRU_SO_EXT", "").strip()
                full_address = f"{dia_chi}, {xa}, {tinh}".strip(", ")
                return full_address
        except Exception as e:
            return f"không lấy được địa chỉ (lỗi parse: {e})"
        return "không lấy được địa chỉ"

class EmojiAnimationLabel(QLabel):
    """QLabel hiển thị một chuỗi emoji động."""
    def __init__(self, emojis, delay=200, size=32, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.emojis = emojis
        self.idx = 0
        self.setFont(QFont("Segoe UI Emoji", size // 2))
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.animate)
        self.timer.start(delay)
        self.animate()

    def animate(self):
        self.setText(self.emojis[self.idx])
        self.idx = (self.idx + 1) % len(self.emojis)

class FlippingGIFLabel(QLabel):
    """QLabel hiển thị GIF động có thể lật qua lại."""
    def __init__(self, gif_path, flip_interval=2000, size=(32, 32), *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.movie = QMovie(gif_path)
        self.movie.setScaledSize(QSize(*size))
        
        # Kết nối signal frameChanged để xử lý mỗi khung hình
        self.movie.frameChanged.connect(self.on_frame_changed)
        
        # Timer để lật ảnh
        self.flipper = QTimer(self)
        self.flipper.timeout.connect(self.toggle_flip)
        self.flipper.start(flip_interval)
        self.is_flipped = False
        
        self.movie.start()

    def toggle_flip(self):
        """Đảo ngược trạng thái lật."""
        self.is_flipped = not self.is_flipped
        # Cập nhật lại khung hình hiện tại với trạng thái lật mới
        self.on_frame_changed()

    @Slot(int)
    def on_frame_changed(self, frame_number=-1):
        """Được gọi mỗi khi QMovie có khung hình mới."""
        pixmap = self.movie.currentPixmap()
        if self.is_flipped:
            # Lật ảnh theo chiều ngang
            pixmap = pixmap.transformed(QTransform().scale(-1, 1))
        
        self.setPixmap(pixmap)

    def destroyEvent(self, event):
        """Dọn dẹp timer và movie khi widget bị hủy."""
        if self.movie:
            self.movie.stop()
        if self.flipper:
            self.flipper.stop()
        super().destroyEvent(event)


class HardwareDetectionDialog(QDialog):
    """Dialog hiển thị tiến trình detect phần cứng."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Đang kiểm tra phần cứng...")
        self.setFixedSize(400, 180)
        self.setModal(True)
        self.setWindowFlag(Qt.WindowCloseButtonHint, False)

        layout = QVBoxLayout(self)

        # Icon và tiêu đề
        title_layout = QHBoxLayout()
        self.emoji = EmojiAnimationLabel(["🔍", "🔧", "⚙️"], delay=400, size=32, parent=self)
        title_layout.addWidget(self.emoji)

        title_label = QLabel("Đang kiểm tra phần cứng hệ thống", self)
        title_label.setFont(QFont("", 12, QFont.Bold))
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        layout.addLayout(title_layout)

        # Thông tin chi tiết
        self.detail_label = QLabel("Đang detect CPU và GPU...", self)
        self.detail_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.detail_label)

        # Progress bar
        self.progress = QProgressBar(self)
        self.progress.setRange(0, 100)
        layout.addWidget(self.progress)

        # Thông tin phần cứng tìm được
        self.hardware_info_label = QLabel("", self)
        self.hardware_info_label.setAlignment(Qt.AlignCenter)
        self.hardware_info_label.setStyleSheet("color: green; font-weight: bold;")
        layout.addWidget(self.hardware_info_label)

    def update_progress(self, value, message="", hardware_info=""):
        self.progress.setValue(value)
        if message:
            self.detail_label.setText(message)
        if hardware_info:
            self.hardware_info_label.setText(hardware_info)
        if value >= 100:
            QTimer.singleShot(500, self.accept)  # Đợi 0.5s để user thấy kết quả

class LoadingModelDialog(QDialog):
    """Dialog hiển thị tiến trình tải model TrOCR."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Đang tải model TrOCR...")
        self.setFixedSize(320, 140)
        self.setModal(True)
        self.setWindowFlag(Qt.WindowCloseButtonHint, False)

        layout = QVBoxLayout(self)
        self.emoji = EmojiAnimationLabel(["⏳", "⌛"], delay=300, size=32, parent=self)
        self.emoji.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.emoji)

        self.label = QLabel("Đang tải model TrOCR, vui lòng chờ...", self)
        self.label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label)

        self.progress = QProgressBar(self)
        self.progress.setRange(0, 100)
        layout.addWidget(self.progress)

    @Slot(int)
    def update_progress(self, value):
        self.progress.setValue(value)
        if value == 100:
            self.accept()

    def set_error(self, message):
        self.label.setText(f"Lỗi: {message}")
        self.setWindowFlag(Qt.WindowCloseButtonHint, True) # Cho phép đóng khi lỗi
        self.update()


class MainWindow(QMainWindow):
    MST_COUNT_PREFIX = "📊 Số MST trong bảng: "
    VERSION = "5.0.16"

    def __init__(self):
        super().__init__()
        self._model_ready = False  # Thêm biến trạng thái model
        # Cập nhật version
        self.setWindowTitle(f'Tra cứu thuế v{self.VERSION} (TNT)')
        self.setWindowIcon(QIcon("default.ico"))

        # Khởi tạo các biến trạng thái
        self.setup_state()
        # Khởi tạo UI
        self.setup_ui()
        # Kết nối các signals và slots
        self.setup_connections()
        
        # Cập nhật thông tin phần cứng và tải model
        QTimer.singleShot(100, self.update_hardware_info)
        self.load_model_bg()

        # Kiểm tra file setting
        self.check_setting_file()
        self.update_mst_count()

    def setup_state(self):
        self.output_lines_full: Optional[List[str]] = None
        self.update_indices: Optional[List[int]] = None
        self.danh_sach: List[str] = []
        self.ketqua_file: str = ""
        self.start_time: Optional[float] = None
        self.is_paused: bool = False
        self.active_threads = 0
        self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0
        self.total = 0
        self.debug_flags = {'save_response': False, 'wrong_captcha': False, 'always_fail': False}

    def setup_ui(self):
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # 1. Hướng dẫn và Số lượng MST
        top_layout = QHBoxLayout()
        top_layout.addWidget(QLabel("📋 Mỗi dòng 1 MST (không chứa khoảng trắng/tab)"))
        top_layout.addStretch()
        self.mst_count_label = QLabel(f"{self.MST_COUNT_PREFIX}0")
        top_layout.addWidget(self.mst_count_label)
        self.main_layout.addLayout(top_layout)

        # 2. Textbox nhập liệu
        self.text_box = QTextEdit()
        self.text_box.setLineWrapMode(QTextEdit.NoWrap) # Tắt chế độ tự động xuống dòng
        self.text_box.setPlaceholderText("Dán danh sách MST vào đây...")
        self.text_box.setMinimumHeight(150)
        self.main_layout.addWidget(self.text_box)

        # 3. Cài đặt luồng (canh giữa)
        workers_layout = QHBoxLayout()
        workers_layout.addStretch()
        workers_layout.addWidget(QLabel("Số luồng: (có thể thay đổi khi đang chạy)"))
        self.entry_workers = QLineEdit(str(default_workers))
        self.entry_workers.setFixedWidth(50)
        workers_layout.addWidget(self.entry_workers)
        workers_layout.addWidget(QLabel("(thấy máy lag thì hạ xuống)"))
        workers_layout.addStretch()
        self.main_layout.addLayout(workers_layout)

        # 4. Các nút chức năng
        btn_layout = QHBoxLayout()
        self.btn_start = QPushButton("🚀 Bắt đầu tra")
        self.btn_pause = QPushButton("⏸ Tạm dừng")
        self.btn_pause.setEnabled(False)
        self.btn_retry = QPushButton("🔁 Làm lại từ kết quả cũ")
        self.btn_open_result = QPushButton("📄 Mở file kết quả")
        self.btn_open_result.setEnabled(False)
        self.btn_open_folder = QPushButton("📁 Mở thư mục")
        self.btn_debug = QPushButton("🐞 Debug")
        self.btn_exit = QPushButton("❌ Thoát")
        
        btn_layout.addWidget(self.btn_start)
        btn_layout.addWidget(self.btn_pause)
        btn_layout.addWidget(self.btn_retry)
        btn_layout.addWidget(self.btn_open_result)
        btn_layout.addWidget(self.btn_open_folder)
        btn_layout.addWidget(self.btn_debug)
        btn_layout.addWidget(self.btn_exit)
        self.main_layout.addLayout(btn_layout)

        # 5. Khung trạng thái tổng hợp (1 dòng)
        status_frame = QFrame()
        status_frame.setFrameShape(QFrame.StyledPanel)
        status_layout = QHBoxLayout(status_frame)

        # Model status
        self.model_status_label = QLabel("Đang tải model...")
        self.model_status_label.setStyleSheet("color: orange;")

        # Kết quả (đang chạy...)
        self.kq_frame = QHBoxLayout()
        self.label_kq = QLabel("Đang dừng")
        self.label_kq.setStyleSheet("color: gray;")
        self.kq_frame.addWidget(self.label_kq)
        self.kq_widget = None

        # Thời gian
        self.time_label = QLabel("⏱ Thời gian: 00:00:00")

        # Hardware status
        self.hardware_status_label = QLabel("🔍 Đang detect phần cứng...")
        self.hardware_status_label.setStyleSheet("color: blue; font-weight: bold;")
        self.hardware_status_label.setCursor(Qt.PointingHandCursor)
        self.hardware_status_label.mousePressEvent = self.on_hardware_status_clicked

        # Separators
        sep1 = QLabel(" | ")
        sep1.setStyleSheet("color: gray;")
        sep2 = QLabel(" | ")
        sep2.setStyleSheet("color: gray;")
        sep3 = QLabel(" | ")
        sep3.setStyleSheet("color: gray;")

        # Layout: Model | Đang chạy... | Thời gian | Hardware (canh giữa)
        status_layout.addStretch()
        status_layout.addWidget(self.model_status_label)
        status_layout.addWidget(sep1)
        status_layout.addLayout(self.kq_frame)
        status_layout.addWidget(sep2)
        status_layout.addWidget(self.time_label)
        status_layout.addWidget(sep3)
        status_layout.addWidget(self.hardware_status_label)
        status_layout.addStretch()

        self.main_layout.addWidget(status_frame)

        # 6. Label trạng thái chi tiết
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.main_layout.addWidget(self.status_label)

        # 9. Log box
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        self.main_layout.addWidget(self.log_box)

        # Timer cho đồng hồ
        self.timer = QTimer(self)
        
        self.update_status()

    def setup_connections(self):
        self.text_box.textChanged.connect(self.update_mst_count)
        self.entry_workers.editingFinished.connect(self.on_workers_change)
        
        self.btn_start.clicked.connect(self.bat_dau)
        self.btn_pause.clicked.connect(self.toggle_pause)
        self.btn_retry.clicked.connect(self.lam_lai)
        self.btn_open_result.clicked.connect(self.open_result_file_directly)
        self.btn_open_folder.clicked.connect(self.open_result_folder)
        self.btn_debug.clicked.connect(self.open_debug_window)
        self.btn_exit.clicked.connect(self.close)

        self.timer.timeout.connect(self.update_time_display)

    def closeEvent(self, event):
        reply = QMessageBox.question(self, 'Xác nhận thoát', 'Bạn có chắc muốn thoát chương trình không?',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

    # =====================================================================
    # CÁC HÀM XỬ LÝ LOGIC (Đã được chuyển đổi sang PySide6)
    # =====================================================================
    @Slot()
    def log(self, msg: str):
        self.log_box.append(msg)
    
    @Slot()
    def update_mst_count(self):
        content = self.text_box.toPlainText().strip()
        if content:
            lines = [line.strip() for line in content.splitlines() if line.strip()]
            count = len(lines)
            self.mst_count_label.setText(f"{self.MST_COUNT_PREFIX}{count}")
        else:
            self.mst_count_label.setText(f"{self.MST_COUNT_PREFIX}0")

    @Slot()
    def update_status(self):
        captcha_total = self.captcha_ok + self.captcha_fail
        percent = (self.captcha_ok * 100 / captcha_total) if captcha_total > 0 else 0
        captcha_info = f"🧩 Captcha đúng: {self.captcha_ok}/{captcha_total} ({percent:.1f}%)"
        
        max_workers = QThreadPool.globalInstance().maxThreadCount()
        status_text = (f"✔ Thành công: {self.ok}   ❌ Thất bại: {self.fail}   {captcha_info}   "
                       f"✍️ Đã xử lý: {self.done} / {self.total}   "
                       f"👨‍👩‍👦‍👦 Luồng: {self.active_threads}/{max_workers}")
        self.status_label.setText(status_text)
        
    @Slot()
    def update_time_display(self):
        if self.start_time:
            elapsed = int(time.time() - self.start_time)
            hours, remainder = divmod(elapsed, 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.setText(f"⏱ {hours:02d}:{minutes:02d}:{seconds:02d}")

    def load_model_bg(self):
        # self.btn_start.setEnabled(False)  # Không disable nút bắt đầu nữa
        self.model_status_label.setText("Tải model...")
        self.model_status_label.setStyleSheet("color: orange;")

        class ModelLoader(QRunnable):
            def __init__(self, signals):
                super().__init__()
                self.signals = signals
            def run(self):
                try:
                    # Sử dụng callback để báo cáo tiến trình
                    load_model(progress_callback=self.signals.progress.emit) 
                    self.signals.finished.emit()
                except Exception as e:
                    self.signals.error.emit(str(e))
        
        class ModelSignals(QObject):
            progress = Signal(int) # Thêm signal cho tiến trình
            finished = Signal()
            error = Signal(str)
            
        self.model_signals = ModelSignals()
        self.model_signals.progress.connect(self.on_model_progress) # Kết nối signal
        self.model_signals.finished.connect(self.on_model_loaded)
        self.model_signals.error.connect(self.on_model_load_error)
        
        # Chạy trong background thread pool
        QThreadPool.globalInstance().start(ModelLoader(self.model_signals))

    @Slot(int)
    def on_model_progress(self, percent):
        """Cập nhật label trạng thái với % tải."""
        self._last_model_percent = percent
        if hasattr(self, '_pending_start') and self._pending_start:
            self.model_status_label.setText(f"Tải model... Sẽ tự động bắt đầu ({percent}%)")
        else:
            self.model_status_label.setText(f"Tải model... {percent}%")

    @Slot()
    def on_model_loaded(self):
        self.model_status_label.setText("✅ Model sẵn sàng")
        self.model_status_label.setStyleSheet("color: green;")
        self._model_ready = True
        # self.btn_start.setEnabled(True)  # Không cần enable lại vì luôn sáng
        # Nếu có pending_start thì tự động bắt đầu
        if hasattr(self, '_pending_start') and self._pending_start:
            self._pending_start = False
            self.bat_dau()

    @Slot(str)
    def on_model_load_error(self, error_msg):
        self.model_status_label.setText(f"❌ Lỗi model")
        self.model_status_label.setStyleSheet("color: red;")
        QMessageBox.critical(self, "Lỗi nghiêm trọng", f"Không thể tải model TrOCR:\n{error_msg}\nChương trình sẽ thoát.")
        self.close()

    def show_hardware_detection_and_load_model(self):
        """Hiển thị dialog detect phần cứng và tải model"""
        # Tạo và hiển thị hardware detection dialog
        hardware_dialog = HardwareDetectionDialog(self)

        # Chạy hardware detection trong background
        class HardwareDetectionWorker(QRunnable):
            def __init__(self, dialog, main_window):
                super().__init__()
                self.dialog = dialog
                self.main_window = main_window
                self.signals = WorkerSignals()

            def run(self):
                import time
                try:
                    # Step 1: Detect CPU
                    self.dialog.update_progress(20, "Đang detect CPU...")
                    time.sleep(0.3)  # Small delay để user thấy progress
                    hardware_detector.get_cpu_info()

                    # Step 2: Detect NVIDIA GPU
                    self.dialog.update_progress(40, "Đang detect NVIDIA GPU...")
                    time.sleep(0.3)
                    hardware_detector.get_nvidia_gpu_info()

                    # Step 3: Detect Intel GPU
                    self.dialog.update_progress(60, "Đang detect Intel GPU...")
                    time.sleep(0.3)
                    hardware_detector.get_intel_gpu_info()

                    # Step 4: Check PyTorch
                    self.dialog.update_progress(80, "Đang kiểm tra PyTorch...")
                    time.sleep(0.3)
                    hardware_detector.get_pytorch_device_info()

                    # Step 5: Determine best device
                    self.dialog.update_progress(90, "Đang chọn phần cứng tốt nhất...")
                    time.sleep(0.3)
                    hardware_detector.determine_best_device()
                    device_display_name = hardware_detector.get_device_display_name()

                    # Step 6: Complete
                    self.dialog.update_progress(100, "Hoàn thành!", device_display_name)

                    # Emit signal để cập nhật UI
                    self.signals.finished.emit()

                except Exception as e:
                    self.signals.error.emit(str(e))

        # Tạo worker và chạy
        worker = HardwareDetectionWorker(hardware_dialog, self)
        worker.signals.finished.connect(lambda: self.on_hardware_detection_complete(hardware_dialog))
        worker.signals.error.connect(lambda err: self.on_hardware_detection_error(hardware_dialog, err))

        QThreadPool.globalInstance().start(worker)
        hardware_dialog.exec()

    def on_hardware_detection_complete(self, dialog):
        """Xử lý khi hardware detection hoàn thành"""
        # Cập nhật thông tin phần cứng trên UI
        self.update_hardware_info()

        # Bắt đầu tải model
        self.load_model_bg()

    def on_hardware_detection_error(self, dialog, error_msg):
        """Xử lý khi hardware detection lỗi"""
        self.hardware_status_label.setText("❌ Lỗi phần cứng")
        self.hardware_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.log(f"⚠️ Lỗi detect phần cứng: {error_msg}")
        dialog.accept()

        # Vẫn tải model dù detect phần cứng lỗi
        self.load_model_bg()

    def update_hardware_info(self):
        """Cập nhật thông tin phần cứng OCR"""
        try:
            # Lấy thông tin phần cứng
            device_display_name = hardware_detector.get_device_display_name()
            self.hardware_status_label.setText(device_display_name)
            self.hardware_status_label.setStyleSheet("color: green; font-weight: bold;")

            # Log thông tin chi tiết
            device_type, device_name, device_info = hardware_detector.determine_best_device()
            self.log(f"🔧 Phần cứng OCR được chọn: {device_name}")

            if device_type == 'cuda' and 'memory' in device_info:
                self.log(f"   💾 VRAM: {device_info['memory']}")
                if 'compute_capability' in device_info:
                    self.log(f"   ⚡ Compute Capability: {device_info['compute_capability']}")
            elif device_type == 'cpu':
                if 'cores' in device_info:
                    self.log(f"   🧠 CPU Cores: {device_info['cores']}")

        except Exception as e:
            self.hardware_status_label.setText("❌ Lỗi phần cứng")
            self.hardware_status_label.setStyleSheet("color: red; font-weight: bold;")
            self.log(f"⚠️ Lỗi detect phần cứng: {e}")

    def on_hardware_status_clicked(self, event):
        """Xử lý khi click vào hardware status để hiển thị chi tiết"""
        try:
            detailed_info = hardware_detector.get_detailed_info()

            # Tạo thông tin chi tiết
            info_text = "=== THÔNG TIN PHẦN CỨNG CHI TIẾT ===\n\n"

            # CPU
            cpu_info = detailed_info['cpu']
            info_text += f"🖥️ CPU:\n"
            info_text += f"   Name: {cpu_info['name']}\n"
            info_text += f"   Architecture: {cpu_info['architecture']}\n"
            info_text += f"   Cores: {cpu_info['cores']}\n\n"

            # NVIDIA GPUs
            nvidia_gpus = detailed_info['nvidia_gpus']
            info_text += f"🎮 NVIDIA GPUs: {len(nvidia_gpus)}\n"
            for gpu in nvidia_gpus:
                info_text += f"   - {gpu['name']} ({gpu.get('memory', 'Unknown memory')})\n"
            info_text += "\n"

            # Intel GPUs
            intel_gpus = detailed_info['intel_gpus']
            info_text += f"🔷 Intel GPUs: {len(intel_gpus)}\n"
            for gpu in intel_gpus:
                info_text += f"   - {gpu['name']} ({gpu.get('memory', 'Unknown')} MB)\n"
            info_text += "\n"

            # PyTorch
            pytorch_info = detailed_info['pytorch']
            info_text += f"🔥 PyTorch:\n"
            info_text += f"   Available: {pytorch_info['torch_available']}\n"
            info_text += f"   CUDA: {pytorch_info['cuda_available']}\n"
            info_text += f"   XPU: {pytorch_info.get('xpu_available', False)}\n"
            info_text += f"   Other backends: {pytorch_info.get('other_backends', [])}\n\n"

            # Best device
            device_type, device_name, device_info = detailed_info['best_device']
            info_text += f"🏆 Best Device for OCR:\n"
            info_text += f"   Type: {device_type}\n"
            info_text += f"   Name: {device_name}\n"
            info_text += f"   Display: {hardware_detector.get_device_display_name()}\n"

            # Hiển thị dialog
            QMessageBox.information(self, "Thông tin phần cứng", info_text)

        except Exception as e:
            QMessageBox.warning(self, "Lỗi", f"Không thể lấy thông tin phần cứng:\n{e}")

    def check_setting_file(self):
        """Kiểm tra và thông báo về file setting.ini"""
        if os.path.exists("setting.ini"):
            try:
                test_cfg = configparser.ConfigParser()
                test_cfg.read("setting.ini", encoding="utf-8")
                workers = test_cfg.getint("DEFAULT", "max_workers", fallback=None)
                retry = test_cfg.getint("DEFAULT", "retry_failed", fallback=None)
                timeout = test_cfg.getint("DEFAULT", "request_timeout", fallback=None)
                ocr_timeout = test_cfg.getint("DEFAULT", "ocr_timeout", fallback=None)
                if workers is not None and retry is not None and timeout is not None and ocr_timeout is not None:
                    self.log("✅ File setting.ini đã được tìm thấy và hợp lệ")
                    self.log(f"   - Số luồng mặc định: {workers}")
                    self.log(f"   - Số lần thử lại: {retry}")
                    self.log(f"   - Timeout request: {timeout}s")
                    self.log(f"   - Timeout OCR captcha: {ocr_timeout}s")
                else:
                    self.log("⚠️ File setting.ini tồn tại nhưng thiếu một số cấu hình")
                    self.log("   Sử dụng giá trị mặc định")
            except Exception as e:
                self.log(f"⚠️ File setting.ini có lỗi: {e}")
                self.log("   Sử dụng giá trị mặc định")
        else:
            self.log("❌ Không tìm thấy file setting.ini")
            self.log("   Tạo file setting.ini với cấu hình mặc định...")
            self.create_default_setting_file()

    def create_default_setting_file(self):
        """Tạo file setting.ini với cấu hình mặc định"""
        try:
            default_cfg = configparser.ConfigParser()
            default_cfg['DEFAULT'] = {
                'max_workers': '20',
                'retry_failed': '20',
                'request_timeout': '120',
                'ocr_timeout': '120'
            }
            with open('setting.ini', 'w', encoding='utf-8') as f:
                default_cfg.write(f)
            self.log("✅ Đã tạo file setting.ini với cấu hình mặc định")
        except Exception as e:
            self.log(f"❌ Không thể tạo file setting.ini: {e}")

    def save_workers_to_setting(self, workers_count):
        """Lưu số luồng vào file setting.ini"""
        global default_workers, cfg
        try:
            # Đọc file setting hiện tại hoặc tạo mới nếu không tồn tại
            setting_cfg = configparser.ConfigParser()
            if os.path.exists('setting.ini'):
                setting_cfg.read('setting.ini', encoding='utf-8')

            # Đảm bảo có section DEFAULT
            if 'DEFAULT' not in setting_cfg:
                setting_cfg['DEFAULT'] = {}

            # Cập nhật số luồng
            setting_cfg['DEFAULT']['max_workers'] = str(workers_count)

            # Ghi lại file
            with open('setting.ini', 'w', encoding='utf-8') as f:
                setting_cfg.write(f)

            # Cập nhật biến global
            default_workers = workers_count
            cfg.set('DEFAULT', 'max_workers', str(workers_count))

            self.log(f"💾 Đã lưu số luồng {workers_count} vào file setting.ini")

        except Exception as e:
            self.log(f"⚠️ Lỗi khi lưu setting: {e}")

    @Slot()
    def on_workers_change(self):
        value = self.entry_workers.text().strip()
        if value.isdigit() and int(value) > 0:
            new_workers = int(value)
            current_max = QThreadPool.globalInstance().maxThreadCount()
            if new_workers != current_max:
                reply = QMessageBox.question(self, "Xác nhận", f"Bạn có muốn cập nhật số luồng từ {current_max} thành {new_workers} không?")
                if reply == QMessageBox.Yes:
                    QThreadPool.globalInstance().setMaxThreadCount(new_workers)
                    self.log(f"🔧 Đã cập nhật số luồng: {new_workers}")
                    # Lưu số luồng mới vào file setting
                    self.save_workers_to_setting(new_workers)
                    self.update_status()
                else:
                    self.entry_workers.setText(str(current_max))
        else:
            self.entry_workers.setText(str(QThreadPool.globalInstance().maxThreadCount()))

    @Slot()
    def toggle_pause(self):
        self.is_paused = not self.is_paused
        if self.is_paused:
            self.btn_pause.setText("▶️ Chạy tiếp")
            self.log("⏸ Đã tạm dừng. Các luồng đang chạy sẽ hoàn thành nốt.")
            QThreadPool.globalInstance().setMaxThreadCount(0) # Tạm dừng gửi task mới
        else:
            self.btn_pause.setText("⏸ Tạm dừng")
            max_threads = int(self.entry_workers.text())
            self.log(f"▶️ Đã chạy tiếp với {max_threads} luồng.")
            QThreadPool.globalInstance().setMaxThreadCount(max_threads)
            self.process_queue()
        self.update_status()

    def show_kq_loading(self, show=True):
        if show:
            if self.kq_widget: return
            gif_path = "resource/working.gif"
            if os.path.exists(gif_path):
                 self.kq_widget = FlippingGIFLabel(gif_path, size=(28,28), parent=self)
            else: # Fallback
                self.kq_widget = EmojiAnimationLabel(["🧎🏻‍♂️","🧍🏻‍♂️","🚶🏻‍♂️","🏃🏻‍♂️"], delay=300, size=28, parent=self)
            self.kq_frame.insertWidget(0, self.kq_widget)
            self.label_kq.setText(" Đang chạy...")
            self.label_kq.setStyleSheet("")  # Reset style về mặc định
        else:
            if self.kq_widget:
                self.kq_widget.deleteLater()
                self.kq_widget = None
            self.label_kq.setText("Đang dừng")
            self.label_kq.setStyleSheet("color: gray;")

    def show_kq_done(self):
        self.show_kq_loading(False)
        self.kq_widget = QLabel("🎉", self)
        self.kq_widget.setFont(QFont("Segoe UI Emoji", 16))
        self.kq_frame.insertWidget(0, self.kq_widget)
        self.label_kq.setText(" Đã xong hết rồi!")
        self.label_kq.setStyleSheet("")  # Reset style về mặc định
    
    @Slot(str)
    def handle_worker_error(self, error_msg):
        self.log(error_msg)
        # Có thể thêm logic khác ở đây, ví dụ hiện messagebox
        
    @Slot()
    def bat_dau(self, from_retry=False):
        # =============================================================
        # KIỂM TRA ĐẦU VÀO
        # =============================================================
        mst_text = self.text_box.toPlainText().strip()
        if not mst_text:
            QMessageBox.warning(self, "Lỗi đầu vào", "Vui lòng nhập ít nhất một Mã Số Thuế vào ô trống trước khi bắt đầu.")
            return
        # =============================================================
        if not getattr(self, '_model_ready', False):
            # Nếu model chưa sẵn sàng, chờ model xong rồi mới chạy
            self._pending_start = True
            # Hiển thị % nếu có
            percent = getattr(self, '_last_model_percent', 0)
            self.model_status_label.setText(f"Đang tải model... Sẽ tự động bắt đầu khi model sẵn sàng. {percent}%")
            self.btn_start.setEnabled(False)
            return
        self.btn_start.setEnabled(False)
        self._pending_start = False
        # Reset trạng thái label kq trước
        self.label_kq.setText("")
        self.label_kq.setStyleSheet("")  # Reset style
        if self.kq_widget:
            self.kq_widget.deleteLater()
            self.kq_widget = None

        raw_text = self.text_box.toPlainText()
        danh_sach_raw = [line.strip() for line in raw_text.splitlines() if line.strip()]

        if not danh_sach_raw:
            self.label_kq.setText("❌ Không có MST nào.")
            self.label_kq.setStyleSheet("color: red;")
            return

        for idx, mst in enumerate(danh_sach_raw, 1):
            if " " in mst or "\t" in mst:
                self.label_kq.setText(f"❌ MST dòng {idx} chứa khoảng trắng/tab: [{mst}]")
                self.label_kq.setStyleSheet("color: red;")
                return

        self.danh_sach = danh_sach_raw
        # Reset state
        self.update_indices = None
        self.output_lines_full = None
        self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0
        self.active_threads = 0
        self.total = len(self.danh_sach)
        self.output_lines = [""] * self.total
        self.is_paused = False
        
        # Setup file kết quả
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.ketqua_file = f"ketqua_{timestamp}.txt"
        
        self.log(f"Bắt đầu tra cứu {self.total} MST...")
        self.update_status()
        
        # Bắt đầu timer
        self.start_time = time.time()
        self.timer.start(1000)

        # Cập nhật UI
        self.btn_start.setEnabled(False)
        self.btn_retry.setEnabled(False)
        self.btn_pause.setEnabled(True)
        self.btn_open_result.setEnabled(True)
        self.show_kq_loading(True)

        # Tạo queue và bắt đầu xử lý
        self.task_queue = list(enumerate(self.danh_sach))
        QThreadPool.globalInstance().setMaxThreadCount(int(self.entry_workers.text()))
        self.process_queue()
        
    def process_queue(self):
        while self.task_queue:
            if QThreadPool.globalInstance().activeThreadCount() >= QThreadPool.globalInstance().maxThreadCount():
                break # Chờ luồng trống

            idx, mst = self.task_queue.pop(0)
            worker = TraCuuWorker(mst, idx, self.debug_flags.copy())
            worker.signals.log.connect(self.log)
            worker.signals.update_result.connect(self.on_worker_update)
            worker.signals.captcha_result.connect(self.on_captcha_result)
            worker.signals.task_completed.connect(self.on_task_completed)
            worker.signals.finished.connect(self.on_worker_finished)
            worker.signals.error.connect(self.handle_worker_error)
            
            self.active_threads += 1
            self.log(f"🔧 Số luồng đang hoạt động tăng: {self.active_threads - 1} → {self.active_threads}")
            self.update_status()
            QThreadPool.globalInstance().start(worker)

    @Slot(int, str)
    def on_worker_update(self, idx, text):
        # Cập nhật vào list kết quả
        if self.update_indices:
             real_idx = self.update_indices[idx]
             self.output_lines_full[real_idx] = text
        else:
            self.output_lines[idx] = text
        
        self.write_result_file()
        self.log(text)

    @Slot(bool)
    def on_task_completed(self, is_ok):
        """Chỉ xử lý việc đếm ok/fail ở đây."""
        if is_ok:
            self.ok += 1
        else:
            self.fail += 1
        # Không cần update_status ở đây vì on_worker_finished sẽ làm

    @Slot(bool)
    def on_captcha_result(self, is_ok):
        if is_ok: self.captcha_ok += 1
        else: self.captcha_fail += 1
        self.update_status()
        
    @Slot()
    def on_worker_finished(self):
        self.active_threads -= 1
        self.log(f"🔧 Số luồng đang hoạt động giảm: {self.active_threads + 1} → {self.active_threads}")
        self.done += 1
        self.update_status()

        if not self.is_paused:
            self.process_queue() # Lấy task tiếp theo

        if self.done == self.total:
            self.finish_processing()
            
    def finish_processing(self):
        self.timer.stop()
        self.log("🎯 Hoàn thành xử lý tất cả MST")
        
        self.show_kq_done()
        self.btn_start.setEnabled(True)
        self.btn_retry.setEnabled(True)
        self.btn_pause.setEnabled(False)
        self.is_paused = False
        
        # Thay đổi điều kiện: chỉ hỏi làm lại khi self.fail > 0
        if self.fail > 0:
            reply = QMessageBox.question(self, "Hoàn thành",
                                         "🎉 Đã xong hết rồi!\nCó kết quả không lấy được địa chỉ.\n"
                                         "Bạn có muốn làm lại ngay từ file kết quả này không?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
            if reply == QMessageBox.Yes:
                self._auto_lamlai_from_last_result()
        else:
            reply = QMessageBox.question(self, "Hoàn thành", 
                                         "🎉 Đã xong hết rồi!\nBạn có muốn mở file kết quả không?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
            if reply == QMessageBox.Yes:
                self.open_result_file_directly()

    def write_result_file(self):
        try:
            lines_to_write = self.output_lines_full if self.output_lines_full is not None else self.output_lines
            with open(self.ketqua_file, "w", encoding="utf-8") as f:
                f.write("\n".join(lines_to_write))
        except Exception as e:
            self.log(f"⚠ Lỗi ghi file kết quả: {e}")

    @Slot()
    def open_result_file_directly(self):
        if not self.ketqua_file or not os.path.exists(self.ketqua_file):
            QMessageBox.warning(self, "Thông báo", "Chưa có file kết quả hoặc file đã bị xóa.")
            return
        os.startfile(self.ketqua_file)
        self.log(f"📄 Đã mở file kết quả: {self.ketqua_file}")

    @Slot()
    def open_result_folder(self):
        try:
            if getattr(sys, 'frozen', False):
                app_dir = os.path.dirname(sys.executable)
                os.startfile(app_dir)
                self.log(f"📁 Đã mở thư mục ứng dụng: {app_dir}")
            else:
                script_dir = os.path.dirname(os.path.abspath(__file__))
                os.startfile(script_dir)
                self.log(f"📁 Đã mở thư mục script: {script_dir}")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể mở thư mục:\n{e}")
            self.log(f"❌ Lỗi mở thư mục: {e}")

    @Slot()
    def lam_lai(self):
        retry_dialog = RetryDialog(self)
        if retry_dialog.exec() != QDialog.Accepted:
            return
            
        options = retry_dialog.get_options()
        path = options['path']
        if not path or not os.path.exists(path):
            QMessageBox.warning(self, "Lỗi", f"File không tồn tại hoặc đường dẫn trống:\n{path}")
            return
            
        self.retry_processing(path, options['retry_pending'], options['retry_failed'])

    def retry_processing(self, file_path: str, retry_pending: bool, retry_failed: bool):
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
        except Exception as e:
            QMessageBox.critical(self, "Lỗi đọc file", f"Không thể đọc file kết quả:\n{e}")
            self.log(f"⚠ Lỗi đọc file kết quả: {e}")
            return

        self.output_lines_full = [line.strip() for line in lines]
        
        tasks_to_retry = []
        self.update_indices = []

        for idx, line in enumerate(self.output_lines_full):
            should_retry = False
            # Chuyển sang lower() để bắt case-insensitive
            line_lower = line.lower()
            if retry_pending and "đang kiểm tra" in line_lower:
                should_retry = True
            elif retry_failed and "không lấy được địa chỉ" in line_lower:
                should_retry = True
            
            if should_retry:
                parts = line.strip().split("\t")
                if len(parts) >= 2:
                    mst = parts[1]
                    tasks_to_retry.append(mst)
                    self.update_indices.append(idx)
        
        if not tasks_to_retry:
            QMessageBox.information(self, "Thông báo", "Không có MST nào phù hợp trong file được chọn để tra lại.")
            return

        # Reset state & UI for the new run
        self.danh_sach = tasks_to_retry
        self.ok = self.fail = self.done = self.captcha_fail = self.captcha_ok = 0
        self.active_threads = 0
        self.total = len(self.danh_sach)
        self.is_paused = False
        
        # Create a new result file for the retry
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name, ext = os.path.splitext(os.path.basename(file_path))
        dir_name = os.path.dirname(file_path)
        self.ketqua_file = os.path.join(dir_name, f"{base_name}_lamlai_{timestamp}{ext}")
        
        # We write the full original content to the new file first
        self.write_result_file() 
        self.log(f"🔁 Bắt đầu tra lại từ file: {file_path} ({self.total} MST)")
        
        self.start_time = time.time()
        self.timer.start(1000)

        self.btn_start.setEnabled(False)
        self.btn_retry.setEnabled(False)
        self.btn_pause.setEnabled(True)
        self.btn_open_result.setEnabled(True)
        self.show_kq_loading(True)

        # Cần reset lại output_lines cho lần chạy lại này, map từ index của task_queue sang index của file gốc
        self.output_lines = [""] * len(self.danh_sach)
        self.task_queue = list(enumerate(self.danh_sach))
        
        QThreadPool.globalInstance().setMaxThreadCount(int(self.entry_workers.text()))
        self.process_queue()

    @Slot()
    def open_debug_window(self):
        debug_dialog = DebugDialog(self.debug_flags, self)
        if debug_dialog.exec() == QDialog.Accepted:
            self.debug_flags = debug_dialog.new_flags
            QMessageBox.information(self, "Debug", "Chế độ debug đã được lưu cho lần tra cứu tiếp theo!")
        
    def _auto_lamlai_from_last_result(self):
        # Automatically retry failed and pending items from the last run
        self.retry_processing(self.ketqua_file, retry_pending=True, retry_failed=True)

class DebugDialog(QDialog):
    """Dialog cho các công cụ debug."""
    def __init__(self, current_flags, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Debug Tools")
        self.setFixedSize(400, 250)
        self.setModal(True)

        self.new_flags = current_flags.copy()

        layout = QVBoxLayout(self)

        info_label = QLabel("Tính năng này dành cho việc kiểm tra lỗi, trong ngữ cảnh bình thường vui lòng không sử dụng")
        info_label.setStyleSheet("color: red;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        self.save_cb = QCheckBox("Lưu file trả về (raw HTML/json)")
        self.save_cb.setChecked(self.new_flags.get('save_response', False))
        layout.addWidget(self.save_cb)

        self.wrong_cb = QCheckBox("Cố ý sai captcha")
        self.wrong_cb.setChecked(self.new_flags.get('wrong_captcha', False))
        layout.addWidget(self.wrong_cb)

        self.fail_cb = QCheckBox("Kết quả luôn là không tìm được địa chỉ")
        self.fail_cb.setChecked(self.new_flags.get('always_fail', False))
        layout.addWidget(self.fail_cb)
        
        button_layout = QHBoxLayout()
        save_button = QPushButton("Lưu")
        close_button = QPushButton("Đóng")

        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)

        save_button.clicked.connect(self.save_and_close)
        close_button.clicked.connect(self.reject)

    def save_and_close(self):
        self.new_flags['save_response'] = self.save_cb.isChecked()
        self.new_flags['wrong_captcha'] = self.wrong_cb.isChecked()
        self.new_flags['always_fail'] = self.fail_cb.isChecked()
        self.accept()

class RetryDialog(QDialog):
    """Dialog để chọn file và các tùy chọn làm lại."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔁 Làm lại từ kết quả cũ")
        self.setMinimumWidth(450)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Chọn file kết quả cũ (.txt):"))

        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("Đường dẫn tới file kết quả...")
        path_layout.addWidget(self.path_edit)
        browse_button = QPushButton("Chọn file")
        browse_button.clicked.connect(self.browse_file)
        path_layout.addWidget(browse_button)
        layout.addLayout(path_layout)
        
        self.retry_pending_cb = QCheckBox("Tra cứu lại các kết quả 'Đang kiểm tra'")
        self.retry_pending_cb.setChecked(True)
        layout.addWidget(self.retry_pending_cb)

        self.retry_failed_cb = QCheckBox("Tra cứu lại các kết quả 'không lấy được địa chỉ'")
        self.retry_failed_cb.setChecked(True)
        layout.addWidget(self.retry_failed_cb)

        layout.addWidget(QLabel("➡️ Chức năng này cho phép tra lại những dòng chưa có kết quả."))

        button_layout = QHBoxLayout()
        start_button = QPushButton("🚀 Bắt đầu")
        cancel_button = QPushButton("Hủy")
        button_layout.addStretch()
        button_layout.addWidget(start_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
        start_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)

    def browse_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Chọn file kết quả", "", "Text files (*.txt)")
        if file_path:
            self.path_edit.setText(file_path)

    def get_options(self):
        return {
            "path": self.path_edit.text(),
            "retry_pending": self.retry_pending_cb.isChecked(),
            "retry_failed": self.retry_failed_cb.isChecked()
        }

def main():
    app = QApplication(sys.argv)
    
    # Tăng cỡ chữ chung cho toàn bộ ứng dụng
    font = app.font()
    font.setPointSize(font.pointSize() + 2)
    app.setFont(font)

    # Cấu hình thread pool toàn cục
    QThreadPool.globalInstance().setMaxThreadCount(default_workers)
    
    window = MainWindow()
    window.resize(800, 600)
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
