#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test Script với dữ liệu demo
Phiên bản: 1.0.0
"""

import sys
import os

# Thêm thư mục gốc vào path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from address_comparison_tool import AddressComparator


def test_demo_data():
    """Test với dữ liệu demo"""
    print("=" * 80)
    print("🏠 TEST VỚI DỮ LIỆU DEMO")
    print("=" * 80)
    
    # Đọc file demo
    demo_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "demo_address_data.txt")
    
    if not os.path.exists(demo_file):
        print(f"❌ Không tìm thấy file demo: {demo_file}")
        return
    
    with open(demo_file, 'r', encoding='utf-8') as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    
    if len(lines) < 2:
        print("❌ File demo không có đủ dữ liệu")
        return
    
    comparator = AddressComparator()
    
    # So sánh từng cặp
    pair_num = 1
    for i in range(0, len(lines), 2):
        if i + 1 < len(lines):
            addr1 = lines[i]
            addr2 = lines[i + 1]
            
            print(f"\n📍 Cặp địa chỉ #{pair_num}:")
            print(f"Địa chỉ 1: {addr1}")
            print(f"Địa chỉ 2: {addr2}")
            
            similarity, details = comparator.calculate_similarity(addr1, addr2)
            
            if similarity >= 0.8:
                result = "✅ GIỐNG NHAU"
                color = "🟢"
            elif similarity >= 0.6:
                result = "⚠️ TƯƠNG TỰ"
                color = "🟡"
            else:
                result = "❌ KHÁC NHAU"
                color = "🔴"
            
            print(f"Kết quả: {color} {result} (Độ tương đồng: {similarity:.1%})")
            
            # Hiển thị địa chỉ chuẩn hóa
            if 'normalized_addr1' in details and 'normalized_addr2' in details:
                print(f"Chuẩn hóa 1: {details['normalized_addr1']}")
                print(f"Chuẩn hóa 2: {details['normalized_addr2']}")
            
            pair_num += 1
            
            # Thêm dòng phân cách
            print("-" * 80)


def generate_summary():
    """Tạo báo cáo tổng kết"""
    print("\n" + "=" * 80)
    print("📊 BÁO CÁO TỔNG KẾT")
    print("=" * 80)
    
    # Đọc file demo
    demo_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "demo_address_data.txt")
    
    with open(demo_file, 'r', encoding='utf-8') as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    
    comparator = AddressComparator()
    
    results = {
        'giống_nhau': 0,
        'tương_tự': 0,
        'khác_nhau': 0,
        'total': 0
    }
    
    # Phân tích tất cả các cặp
    for i in range(0, len(lines), 2):
        if i + 1 < len(lines):
            addr1 = lines[i]
            addr2 = lines[i + 1]
            
            similarity, _ = comparator.calculate_similarity(addr1, addr2)
            
            if similarity >= 0.8:
                results['giống_nhau'] += 1
            elif similarity >= 0.6:
                results['tương_tự'] += 1
            else:
                results['khác_nhau'] += 1
            
            results['total'] += 1
    
    print(f"Tổng số cặp địa chỉ: {results['total']}")
    print(f"✅ Giống nhau: {results['giống_nhau']} ({results['giống_nhau']/results['total']*100:.1f}%)")
    print(f"⚠️ Tương tự: {results['tương_tự']} ({results['tương_tự']/results['total']*100:.1f}%)")
    print(f"❌ Khác nhau: {results['khác_nhau']} ({results['khác_nhau']/results['total']*100:.1f}%)")


def main():
    """Hàm main"""
    print("🚀 BẮT ĐẦU TEST VỚI DỮ LIỆU DEMO")
    
    try:
        test_demo_data()
        generate_summary()
        
        print("\n" + "=" * 80)
        print("✅ TEST HOÀN THÀNH THÀNH CÔNG!")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ LỖI TRONG QUÁ TRÌNH TEST: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
