@echo off
chcp 65001 >nul
title Test Address Comparison Tool

echo.
echo ========================================
echo    🧪 TEST ADDRESS COMPARISON TOOL
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 Kiểm tra Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python không được tìm thấy!
    pause
    exit /b 1
)

echo ✅ Python đã được cài đặt

echo.
echo 🚀 Chạy unit tests...
echo.

python test_address_comparison.py

if errorlevel 1 (
    echo.
    echo ❌ Có lỗi xảy ra trong quá trình test!
) else (
    echo.
    echo ✅ Tất cả tests đã hoàn thành!
)

echo.
pause
