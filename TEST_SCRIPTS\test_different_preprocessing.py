#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test different preprocessing approaches for the Keras model
"""

import os
import sys
import numpy as np
from PIL import Image

# Thêm thư mục gốc vào path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)

def test_different_preprocessing():
    """Test different preprocessing approaches"""
    
    try:
        from kiem_tra_mst_qt import load_model, CHARACTERS
        import tensorflow as tf
        
        print("🔄 Loading model...")
        load_model()
        
        from kiem_tra_mst_qt import _model, _device
        
        # Test với một ảnh cụ thể
        train_data_dir = os.path.join(BASE_DIR, "train_data")
        if os.path.exists(train_data_dir):
            image_files = [f for f in os.listdir(train_data_dir) if f.endswith('.png')]
            if image_files:
                test_image = image_files[0]
                image_path = os.path.join(train_data_dir, test_image)
                expected_label = os.path.splitext(test_image)[0]
                
                print(f"🖼️ Test image: {test_image}")
                print(f"📝 Expected: {expected_label}")
                
                # Load original image
                original_image = Image.open(image_path)
                print(f"📏 Original size: {original_image.size}")
                
                # Test different preprocessing approaches
                preprocessing_methods = {
                    "current": lambda img: preprocess_current(img),
                    "no_normalize": lambda img: preprocess_no_normalize(img),
                    "different_normalize": lambda img: preprocess_different_normalize(img),
                    "binary_threshold": lambda img: preprocess_binary(img),
                    "invert_colors": lambda img: preprocess_invert(img),
                    "different_size": lambda img: preprocess_different_size(img),
                }
                
                for method_name, preprocess_func in preprocessing_methods.items():
                    print(f"\n🔧 Testing {method_name}:")
                    
                    try:
                        # Preprocess
                        img_array = preprocess_func(original_image)
                        print(f"   Shape: {img_array.shape}")
                        print(f"   Range: [{img_array.min():.3f}, {img_array.max():.3f}]")
                        
                        # Predict
                        with tf.device(_device):
                            predictions = _model.predict(img_array, verbose=0)
                        
                        # Decode
                        result = decode_simple_ctc(predictions)
                        match = "✅" if result.lower() == expected_label.lower() else "❌"
                        print(f"   Result: '{result}' {match}")
                        
                    except Exception as e:
                        print(f"   ❌ Error: {e}")
                
        else:
            print("❌ Không tìm thấy thư mục train_data")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

def preprocess_current(image):
    """Current preprocessing method"""
    # Debug: save intermediate steps
    print(f"      Original mode: {image.mode}, size: {image.size}")

    image = image.convert('L')
    print(f"      After convert L: mode: {image.mode}, size: {image.size}")

    image = image.resize((50, 200), Image.Resampling.LANCZOS)
    print(f"      After resize: size: {image.size}")

    img_array = np.array(image, dtype=np.float32)
    print(f"      Array before norm: shape={img_array.shape}, range=[{img_array.min()}, {img_array.max()}]")

    img_array = img_array / 255.0
    img_array = np.expand_dims(img_array, axis=-1)
    img_array = np.expand_dims(img_array, axis=0)
    return img_array

def preprocess_no_normalize(image):
    """No normalization - keep values 0-255"""
    image = image.convert('L')
    image = image.resize((50, 200), Image.Resampling.LANCZOS)
    img_array = np.array(image, dtype=np.float32)  # No division by 255
    img_array = np.expand_dims(img_array, axis=-1)
    img_array = np.expand_dims(img_array, axis=0)
    return img_array

def preprocess_different_normalize(image):
    """Different normalization - mean/std like TrOCR"""
    image = image.convert('L')
    image = image.resize((50, 200), Image.Resampling.LANCZOS)
    img_array = np.array(image, dtype=np.float32)
    # Normalize like TrOCR: (x - 0.5) / 0.5
    img_array = img_array / 255.0  # First to [0, 1]
    img_array = (img_array - 0.5) / 0.5  # Then to [-1, 1]
    img_array = np.expand_dims(img_array, axis=-1)
    img_array = np.expand_dims(img_array, axis=0)
    return img_array

def preprocess_binary(image):
    """Binary threshold preprocessing"""
    image = image.convert('L')
    image = image.resize((50, 200), Image.Resampling.LANCZOS)
    img_array = np.array(image, dtype=np.float32)
    # Binary threshold at 128
    img_array = (img_array > 128).astype(np.float32)
    img_array = np.expand_dims(img_array, axis=-1)
    img_array = np.expand_dims(img_array, axis=0)
    return img_array

def preprocess_invert(image):
    """Invert colors"""
    image = image.convert('L')
    image = image.resize((50, 200), Image.Resampling.LANCZOS)
    img_array = np.array(image, dtype=np.float32)
    img_array = 255 - img_array  # Invert
    img_array = img_array / 255.0
    img_array = np.expand_dims(img_array, axis=-1)
    img_array = np.expand_dims(img_array, axis=0)
    return img_array

def preprocess_different_size(image):
    """Try different size - maybe model expects different dimensions"""
    image = image.convert('L')
    # Try original captcha size or different aspect ratio
    image = image.resize((100, 200), Image.Resampling.LANCZOS)  # Different width
    img_array = np.array(image, dtype=np.float32) / 255.0
    img_array = np.expand_dims(img_array, axis=-1)
    img_array = np.expand_dims(img_array, axis=0)
    return img_array

def decode_simple_ctc(predictions):
    """Simple CTC decoding"""
    try:
        # Define characters locally
        CHARS = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ"

        pred = predictions[0]  # Remove batch dimension
        predicted_classes = np.argmax(pred, axis=1)

        decoded_chars = []
        prev_class = -1

        for class_idx in predicted_classes:
            # Try different blank classes
            if class_idx == 24 or class_idx == len(CHARS):  # blank
                prev_class = class_idx
                continue

            if class_idx != prev_class:
                if class_idx < len(CHARS):
                    decoded_chars.append(CHARS[class_idx])
            prev_class = class_idx

        return ''.join(decoded_chars)
    except Exception as e:
        return f"❌ Decode error: {e}"

if __name__ == "__main__":
    test_different_preprocessing()
