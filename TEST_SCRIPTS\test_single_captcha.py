#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để test một ảnh captcha cụ thể
"""

import os
import sys
import time

# Thêm thư mục gốc vào path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)

def test_single_captcha():
    """Test một ảnh captcha cụ thể"""
    
    try:
        print("🔄 Test single captcha...")
        
        # Import và load model
        from kiem_tra_mst_qt import load_model, solve_captcha
        
        print("🔄 Đang load model...")
        load_model()
        print("✅ Load model thành công")
        
        # Test với một ảnh cụ thể
        train_data_dir = os.path.join(BASE_DIR, "train_data")
        if not os.path.exists(train_data_dir):
            print("❌ Không tìm thấy thư mục train_data")
            return
            
        # L<PERSON>y ảnh đầu tiên
        image_files = [f for f in os.listdir(train_data_dir) if f.endswith('.png')]
        if not image_files:
            print("❌ Không tìm thấy ảnh trong train_data")
            return
            
        test_image = image_files[0]
        image_path = os.path.join(train_data_dir, test_image)
        expected_label = os.path.splitext(test_image)[0]
        
        print(f"\n🖼️ Test ảnh: {test_image}")
        print(f"   Expected: {expected_label}")
        print(f"   Path: {image_path}")
        
        # Predict với debug
        print("🔄 Đang predict...")
        start_time = time.time()
        
        try:
            predicted = solve_captcha(image_path)
            predict_time = time.time() - start_time
            
            print(f"   Predicted: '{predicted}'")
            print(f"   Time: {predict_time:.3f}s")
            
            # So sánh kết quả
            if predicted == expected_label:
                print("   ✅ ĐÚNG!")
            else:
                print("   ❌ SAI!")
                print(f"   Expected: '{expected_label}'")
                print(f"   Got:      '{predicted}'")
                
        except Exception as e:
            print(f"   ❌ Lỗi predict: {e}")
            import traceback
            traceback.print_exc()
            
        print("\n✅ TEST HOÀN THÀNH!")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_captcha()
