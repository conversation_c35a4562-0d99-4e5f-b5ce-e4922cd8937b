#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug script để hiểu rõ hơn về CTC decoding
"""

import os
import sys
import numpy as np

# Thê<PERSON> thư mục gốc vào path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)

def debug_ctc_decoding():
    """Debug CTC decoding với một ảnh cụ thể"""
    
    try:
        from kiem_tra_mst_qt import load_model, preprocess_image_for_keras, CHARACTERS
        import tensorflow as tf
        
        print("🔄 Loading model...")
        load_model()
        
        from kiem_tra_mst_qt import _model, _device
        
        # Test với một ảnh cụ thể
        train_data_dir = os.path.join(BASE_DIR, "train_data")
        if os.path.exists(train_data_dir):
            image_files = [f for f in os.listdir(train_data_dir) if f.endswith('.png')]
            if image_files:
                test_image = image_files[0]  # L<PERSON>y ảnh đầu tiên
                image_path = os.path.join(train_data_dir, test_image)
                expected_label = os.path.splitext(test_image)[0]
                
                print(f"🖼️ Test image: {test_image}")
                print(f"📝 Expected: {expected_label}")
                
                # Preprocess
                img_array = preprocess_image_for_keras(image_path)
                print(f"📏 Preprocessed shape: {img_array.shape}")
                
                # Predict
                with tf.device(_device):
                    predictions = _model.predict(img_array, verbose=0)
                
                print(f"📊 Predictions shape: {predictions.shape}")
                print(f"🔢 Characters available: {len(CHARACTERS)} = {CHARACTERS}")
                print(f"🔢 Prediction classes: {predictions.shape[-1]}")
                
                # Analyze predictions in detail
                pred = predictions[0]  # Remove batch dimension: (50, 25)
                print(f"\n📈 Detailed analysis:")
                print(f"   Time steps: {pred.shape[0]}")
                print(f"   Classes per step: {pred.shape[1]}")
                
                # Show top predictions for each time step
                print(f"\n🔍 Top predictions per time step:")
                for i in range(min(10, pred.shape[0])):  # Show first 10 time steps
                    top_classes = np.argsort(pred[i])[-3:][::-1]  # Top 3 classes
                    top_probs = pred[i][top_classes]
                    
                    print(f"   Step {i:2d}: ", end="")
                    for j, (cls, prob) in enumerate(zip(top_classes, top_probs)):
                        if cls < len(CHARACTERS):
                            char = CHARACTERS[cls]
                        elif cls == 24:  # Assuming blank is class 24
                            char = '<BLANK>'
                        else:
                            char = f'<UNK{cls}>'
                        print(f"{char}({prob:.3f})", end="")
                        if j < len(top_classes) - 1:
                            print(", ", end="")
                    print()
                
                # Try different CTC decoding approaches
                print(f"\n🔧 Testing different CTC decoding approaches:")
                
                # Approach 1: Current implementation
                predicted_classes = np.argmax(pred, axis=1)
                print(f"1. Argmax classes: {predicted_classes}")
                
                decoded_chars = []
                prev_class = -1
                for class_idx in predicted_classes:
                    if class_idx == 24:  # blank
                        prev_class = class_idx
                        continue
                    if class_idx != prev_class:
                        if class_idx < len(CHARACTERS):
                            decoded_chars.append(CHARACTERS[class_idx])
                    prev_class = class_idx
                
                result1 = ''.join(decoded_chars)
                print(f"   Result 1: '{result1}'")
                
                # Approach 2: Use threshold for confidence
                print(f"\n2. With confidence threshold (0.5):")
                decoded_chars2 = []
                prev_class = -1
                for i, probs in enumerate(pred):
                    class_idx = np.argmax(probs)
                    confidence = probs[class_idx]
                    
                    if confidence < 0.5:  # Low confidence, treat as blank
                        prev_class = -1
                        continue
                        
                    if class_idx == 24:  # blank
                        prev_class = class_idx
                        continue
                        
                    if class_idx != prev_class:
                        if class_idx < len(CHARACTERS):
                            decoded_chars2.append(CHARACTERS[class_idx])
                    prev_class = class_idx
                
                result2 = ''.join(decoded_chars2)
                print(f"   Result 2: '{result2}'")
                
                # Approach 3: Different blank class
                print(f"\n3. Assuming blank is last class ({pred.shape[1]-1}):")
                blank_class = pred.shape[1] - 1
                decoded_chars3 = []
                prev_class = -1
                for class_idx in predicted_classes:
                    if class_idx == blank_class:  # blank
                        prev_class = class_idx
                        continue
                    if class_idx != prev_class:
                        if class_idx < len(CHARACTERS):
                            decoded_chars3.append(CHARACTERS[class_idx])
                    prev_class = class_idx
                
                result3 = ''.join(decoded_chars3)
                print(f"   Result 3: '{result3}'")
                
                print(f"\n📊 Summary:")
                print(f"   Expected: '{expected_label}'")
                print(f"   Result 1: '{result1}' {'✅' if result1 == expected_label else '❌'}")
                print(f"   Result 2: '{result2}' {'✅' if result2 == expected_label else '❌'}")
                print(f"   Result 3: '{result3}' {'✅' if result3 == expected_label else '❌'}")
                
        else:
            print("❌ Không tìm thấy thư mục train_data")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_ctc_decoding()
