@echo off
chcp 65001 >nul
title Tool So Sánh Địa Chỉ v1.0.0

echo.
echo ========================================
echo    🏠 TOOL SO SÁNH ĐỊA CHỈ v1.0.0
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🔍 Kiểm tra Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python không được tìm thấy!
    echo Vui lòng cài đặt Python trước khi chạy tool này.
    pause
    exit /b 1
)

echo ✅ Python đã được cài đặt

echo.
echo 📦 Kiểm tra thư viện PySide6...
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ PySide6 chưa được cài đặt
    echo 📥 Đang cài đặt PySide6...
    pip install PySide6
    if errorlevel 1 (
        echo ❌ Không thể cài đặt PySide6!
        pause
        exit /b 1
    )
)

echo ✅ PySide6 đã sẵn sàng

echo.
echo 🚀 Khởi động Tool So Sánh Địa Chỉ...
echo.

python address_comparison_tool.py

if errorlevel 1 (
    echo.
    echo ❌ Có lỗi xảy ra khi chạy tool!
    pause
)

echo.
echo 👋 Tool đã đóng. Cảm ơn bạn đã sử dụng!
pause
