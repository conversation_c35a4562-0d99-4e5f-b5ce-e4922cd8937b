#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test Script cho Address Comparison Tool
Phiên bản: 1.0.0
"""

import sys
import os

# Thêm thư mục gốc vào path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from address_comparison_tool import AddressComparator, AddressNormalizer


def test_normalizer():
    """Test AddressNormalizer"""
    print("=" * 60)
    print("🧪 TEST ADDRESS NORMALIZER")
    print("=" * 60)
    
    normalizer = AddressNormalizer()
    
    test_cases = [
        "Số 269/14 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Việt Nam.",
        "119-121 <PERSON>, <PERSON><PERSON>ờ<PERSON> Bình Phú, Tp.HCM, Việt Nam",
        "546 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> 5, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>.",
        "48 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>",
        "<PERSON>ầng 2, <PERSON>òng 2.11A, Tòa nhà E.Town, số 364, đường Cộng Hòa, Phường Tân Bình, TP Hồ Chí Minh"
    ]
    
    for i, addr in enumerate(test_cases, 1):
        print(f"\n📍 Test case {i}:")
        print(f"Gốc: {addr}")
        normalized = normalizer.normalize_text(addr)
        print(f"Chuẩn hóa: {normalized}")
        
        components = normalizer.extract_components(addr)
        print("Thành phần:")
        for key, value in components.items():
            if value:
                print(f"  - {key}: {value}")


def test_comparator():
    """Test AddressComparator"""
    print("\n" + "=" * 60)
    print("🔍 TEST ADDRESS COMPARATOR")
    print("=" * 60)
    
    comparator = AddressComparator()
    
    test_pairs = [
        (
            "Số 269/14 Gò Dầu, Phường Phú Thọ Hòa, Thành phố Hồ Chí Minh, Việt Nam.",
            "Số 269/14 Gò Dầu, Phường Tân Sơn Nhì, TP Hồ Chí Minh"
        ),
        (
            "119-121 Song Hành, Phường Bình Phú, Tp.HCM, Việt Nam",
            "119-121 Song Hành, Phường Bình Phú, TP Hồ Chí Minh"
        ),
        (
            "546 Quốc Lộ 1A, Khu Phố 5, Phường Bình Tân, TP Hồ Chí Minh.",
            "546 Quốc Lộ 1A, Khu Phố 5, Phường Bình Tân, TP Hồ Chí Minh"
        ),
        (
            "48 Lê Văn Quới, Phường Bình Trị  Đông, Thành phố Hồ Chí Minh, Việt Nam",
            "48 Lê Văn Quới, Phường Bình Trị Đông, TP Hồ Chí Minh"
        ),
        (
            "Tầng 2, Phòng 2.11A, Tòa nhà E.Town, số 364, đường Cộng Hòa, Phường Tân Bình, TP Hồ Chí Minh",
            "Tầng 2, Phòng 2.11A, Tòa nhà E.Town, số 364, đường Cộng Hòa, Phường Tân Bình, TP Hồ Chí Minh"
        )
    ]
    
    for i, (addr1, addr2) in enumerate(test_pairs, 1):
        print(f"\n🏠 Cặp địa chỉ {i}:")
        print(f"Địa chỉ 1: {addr1}")
        print(f"Địa chỉ 2: {addr2}")
        
        similarity, details = comparator.calculate_similarity(addr1, addr2)
        
        if similarity >= 0.8:
            result = "✅ GIỐNG NHAU"
        elif similarity >= 0.6:
            result = "⚠️ TƯƠNG TỰ"
        else:
            result = "❌ KHÁC NHAU"
        
        print(f"Kết quả: {result} (Độ tương đồng: {similarity:.1%})")
        
        if 'component_scores' in details:
            print("Chi tiết điểm số:")
            for component, score in details['component_scores'].items():
                if score > 0:
                    print(f"  - {component}: {score:.1%}")


def test_edge_cases():
    """Test các trường hợp đặc biệt"""
    print("\n" + "=" * 60)
    print("⚠️ TEST EDGE CASES")
    print("=" * 60)
    
    comparator = AddressComparator()
    
    edge_cases = [
        ("", "Địa chỉ bất kỳ"),
        ("Địa chỉ bất kỳ", ""),
        ("", ""),
        ("Địa chỉ hoàn toàn giống nhau", "Địa chỉ hoàn toàn giống nhau"),
        ("A", "B"),
        ("123 ABC", "456 XYZ"),
        ("Số 1 Đường A, Phường B, TP C", "Số 2 Đường D, Phường E, TP F")
    ]
    
    for i, (addr1, addr2) in enumerate(edge_cases, 1):
        print(f"\n🧪 Edge case {i}:")
        print(f"Địa chỉ 1: '{addr1}'")
        print(f"Địa chỉ 2: '{addr2}'")
        
        similarity, details = comparator.calculate_similarity(addr1, addr2)
        print(f"Độ tương đồng: {similarity:.1%}")
        
        if 'error' in details:
            print(f"Lỗi: {details['error']}")


def test_performance():
    """Test hiệu suất"""
    print("\n" + "=" * 60)
    print("⚡ TEST PERFORMANCE")
    print("=" * 60)
    
    import time
    
    comparator = AddressComparator()
    
    # Tạo danh sách địa chỉ test
    addresses = [
        "Số 269/14 Gò Dầu, Phường Phú Thọ Hòa, Thành phố Hồ Chí Minh, Việt Nam.",
        "119-121 Song Hành, Phường Bình Phú, Tp.HCM, Việt Nam",
        "546 Quốc Lộ 1A, Khu Phố 5, Phường Bình Tân, TP Hồ Chí Minh.",
        "48 Lê Văn Quới, Phường Bình Trị  Đông, Thành phố Hồ Chí Minh, Việt Nam",
        "Tầng 2, Phòng 2.11A, Tòa nhà E.Town, số 364, đường Cộng Hòa, Phường Tân Bình, TP Hồ Chí Minh"
    ] * 20  # Nhân 20 để có 100 địa chỉ
    
    start_time = time.time()
    
    comparisons = 0
    for i in range(len(addresses)):
        for j in range(i + 1, min(i + 10, len(addresses))):  # So sánh với 10 địa chỉ tiếp theo
            comparator.calculate_similarity(addresses[i], addresses[j])
            comparisons += 1
    
    end_time = time.time()
    elapsed = end_time - start_time
    
    print(f"Đã thực hiện {comparisons} phép so sánh")
    print(f"Thời gian: {elapsed:.2f} giây")
    print(f"Tốc độ: {comparisons/elapsed:.1f} so sánh/giây")


def main():
    """Hàm main"""
    print("🚀 BẮT ĐẦU TEST ADDRESS COMPARISON TOOL")
    print("=" * 60)
    
    try:
        test_normalizer()
        test_comparator()
        test_edge_cases()
        test_performance()
        
        print("\n" + "=" * 60)
        print("✅ TẤT CẢ TEST ĐÃ HOÀN THÀNH THÀNH CÔNG!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ LỖI TRONG QUÁ TRÌNH TEST: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
