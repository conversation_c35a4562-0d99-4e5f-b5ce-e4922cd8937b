#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script với implementation chuẩn từ captcha_solver_gui.py
"""

import os
import sys
import time

# Thêm thư mục gốc vào path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)

def test_keras_standard():
    """Test Keras model với implementation chuẩn"""
    
    try:
        print("🔄 Test Keras model với implementation chuẩn...")
        print("✅ Import thành công")
        
        # Import và load model
        from kiem_tra_mst_qt import load_model, solve_captcha
        
        print("🔄 Đang load model...")
        start_time = time.time()
        
        def progress_callback(progress):
            print(f"   Progress: {progress}%")
        
        load_model(progress_callback)
        load_time = time.time() - start_time
        print(f"✅ Load model thành công trong {load_time:.2f}s")
        
        # Test với train_data
        train_data_dir = os.path.join(BASE_DIR, "train_data")
        if not os.path.exists(train_data_dir):
            print("❌ Không tìm thấy thư mục train_data")
            return
            
        # Lấy 10 ảnh đầu tiên để test
        image_files = [f for f in os.listdir(train_data_dir) if f.endswith('.png')][:10]
        
        if not image_files:
            print("❌ Không tìm thấy ảnh trong train_data")
            return
            
        print(f"\n🖼️ Test với {len(image_files)} ảnh từ train_data:")
        
        correct = 0
        total = len(image_files)
        
        for i, image_file in enumerate(image_files, 1):
            image_path = os.path.join(train_data_dir, image_file)
            expected_label = os.path.splitext(image_file)[0]
            
            print(f"\n{i}. Test ảnh: {image_file}")
            print(f"   Expected: {expected_label}")
            
            # Predict
            start_time = time.time()
            predicted = solve_captcha(image_path)
            predict_time = time.time() - start_time
            
            print(f"   Predicted: {predicted}")
            print(f"   Time: {predict_time:.3f}s")
            
            # So sánh kết quả
            if predicted == expected_label:
                print("   ✅ ĐÚNG!")
                correct += 1
            else:
                print("   ❌ SAI!")
        
        # Tổng kết
        accuracy = (correct / total) * 100
        print(f"\n📊 KẾT QUẢ TỔNG KẾT:")
        print(f"   Tổng số ảnh: {total}")
        print(f"   Đúng: {correct}")
        print(f"   Sai: {total - correct}")
        print(f"   Độ chính xác: {accuracy:.1f}%")
        
        if accuracy >= 80:
            print("🎉 Model hoạt động TỐT!")
        elif accuracy >= 50:
            print("⚠️ Model hoạt động TRUNG BÌNH")
        else:
            print("❌ Model hoạt động KÉM")
            
        print("\n✅ TEST HOÀN THÀNH!")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_keras_standard()
