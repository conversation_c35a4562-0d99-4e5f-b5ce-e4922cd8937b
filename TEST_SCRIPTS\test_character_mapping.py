#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test different character mappings for the Keras model
"""

import os
import sys
import numpy as np

# Thêm thư mục gốc vào path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, BASE_DIR)

def test_character_mappings():
    """Test different possible character mappings"""
    
    try:
        from kiem_tra_mst_qt import load_model, preprocess_image_for_keras
        import tensorflow as tf
        
        print("🔄 Loading model...")
        load_model()
        
        from kiem_tra_mst_qt import _model, _device
        
        # Different possible character sets
        char_sets = {
            "current": "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ",
            "with_lowercase": "0123456789abcdefghjklmnpqrstuvwxyz",
            "mixed_case": "0123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjklmnpqrstuvwxyz",
            "digits_first": "0123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjklmnpqrstuvwxyz",
            "common_captcha": "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ",
        }
        
        # Test với một ảnh cụ thể
        train_data_dir = os.path.join(BASE_DIR, "train_data")
        if os.path.exists(train_data_dir):
            image_files = [f for f in os.listdir(train_data_dir) if f.endswith('.png')]
            if image_files:
                # Test với vài ảnh khác nhau
                test_images = image_files[:3]
                
                for test_image in test_images:
                    image_path = os.path.join(train_data_dir, test_image)
                    expected_label = os.path.splitext(test_image)[0]
                    
                    print(f"\n🖼️ Test image: {test_image}")
                    print(f"📝 Expected: {expected_label}")
                    
                    # Preprocess và predict
                    img_array = preprocess_image_for_keras(image_path)
                    with tf.device(_device):
                        predictions = _model.predict(img_array, verbose=0)
                    
                    pred = predictions[0]  # (50, 25)
                    predicted_classes = np.argmax(pred, axis=1)
                    
                    print(f"🔢 Raw classes: {predicted_classes[:10]}...")  # Show first 10
                    
                    # Test với các character set khác nhau
                    for name, charset in char_sets.items():
                        if len(charset) >= 25:  # Đảm bảo có đủ ký tự
                            decoded_chars = []
                            prev_class = -1
                            
                            for class_idx in predicted_classes:
                                # Giả sử blank là class cuối cùng
                                if class_idx == 24:  # blank
                                    prev_class = class_idx
                                    continue
                                    
                                if class_idx != prev_class:
                                    if class_idx < len(charset):
                                        decoded_chars.append(charset[class_idx])
                                prev_class = class_idx
                            
                            result = ''.join(decoded_chars)
                            match = "✅" if result.lower() == expected_label.lower() else "❌"
                            print(f"   {name:15}: '{result}' {match}")
                    
                    # Thử nghiệm với confidence threshold
                    print(f"\n🔧 With confidence analysis:")
                    high_conf_classes = []
                    for i, probs in enumerate(pred):
                        max_prob = np.max(probs)
                        if max_prob > 0.8:  # High confidence
                            class_idx = np.argmax(probs)
                            high_conf_classes.append((i, class_idx, max_prob))
                    
                    print(f"   High confidence predictions: {len(high_conf_classes)}")
                    for step, cls, prob in high_conf_classes[:10]:  # Show first 10
                        if cls < 34:
                            char = char_sets["current"][cls]
                        else:
                            char = f"<{cls}>"
                        print(f"      Step {step:2d}: class {cls:2d} = '{char}' ({prob:.3f})")
                    
                    # Thử decode chỉ với high confidence
                    if high_conf_classes:
                        decoded_high_conf = []
                        prev_class = -1
                        for _, class_idx, _ in high_conf_classes:
                            if class_idx == 24:  # blank
                                prev_class = class_idx
                                continue
                            if class_idx != prev_class:
                                if class_idx < len(char_sets["current"]):
                                    decoded_high_conf.append(char_sets["current"][class_idx])
                            prev_class = class_idx
                        
                        high_conf_result = ''.join(decoded_high_conf)
                        match = "✅" if high_conf_result.lower() == expected_label.lower() else "❌"
                        print(f"   High conf result: '{high_conf_result}' {match}")
                
        else:
            print("❌ Không tìm thấy thư mục train_data")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_character_mappings()
